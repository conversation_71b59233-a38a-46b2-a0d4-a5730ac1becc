FROM python:3.11-slim
ARG REPO_URL
ARG GIT_TOKEN
ARG ENV

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false

# Add poetry to PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Install system dependencies
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    curl \
    git \
    build-essential \
    git \
    && rm -rf /var/lib/apt/lists/*


# Install poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Set working directory
WORKDIR /app

# Copy poetry files
COPY pyproject.toml poetry.lock ./

RUN poetry lock

# Install dependencies
RUN poetry install --no-interaction --no-ansi --no-root

# Copy application code
COPY app /app/app

# Generate gRPC stubs
ARG REPO_URL
ARG GIT_TOKEN
ARG ENV

ENV REPO_URL=${REPO_URL}
ENV GIT_TOKEN=${GIT_TOKEN}
ENV ENV=${ENV}
RUN python -m app.scripts.generate_grpc

CMD ["python", "-m", "app.main"] 
