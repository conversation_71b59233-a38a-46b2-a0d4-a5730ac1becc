apiVersion: v1
kind: ServiceAccount
metadata:
  name: call-service-api-sa
  namespace: ruh-ai-dev
  labels:
    name: call-service-api-sa
    namespace: ruh-ai-dev
    app: call-service-api
    deployment: call-service-api-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: call-service-api-dp
  namespace: ruh-ai-dev
  labels:
    name: call-service-api-dp
    namespace: ruh-ai-dev
    app: call-service-api
    serviceaccount: call-service-api-sa
    deployment: call-service-api-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: call-service-api
      deployment: call-service-api-dp
  template:
    metadata:
      labels:
        namespace: ruh-ai-dev
        app: call-service-api
        deployment: call-service-api-dp
    spec:
      serviceAccountName: call-service-api-sa
      containers:
      - name: call-service-api
        image: us-central1-docker.pkg.dev/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<VERSION>
        resources:
          requests:
            memory: 64Mi
            cpu: 50m
          limits:
            memory: 1024Mi
            cpu: 250m
        ports:
        - containerPort: 50058
      #   readinessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 5
      #     periodSeconds: 10
      #   livenessProbe:
      #     tcpSocket:
      #       port: 5001
      #     initialDelaySeconds: 15
      #     periodSeconds: 20
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:    
      #   eks.amazonaws.com/capacityType: SPOT       
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: call-service-ai-svc
  namespace: ruh-ai-dev
spec:
  selector:
    app: call-service-ai
    deployment: call-service-ai-dp
  ports:
    - protocol: TCP
      port: 80
      targetPort: 50058
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA
# apiVersion: autoscaling/v2beta1
# kind: HorizontalPodAutoscaler
# metadata:
#   name: call-service-user-hpa
#   namespace: ruh-ai-dev
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name: call-service-user-dp
#   minReplicas: 1
#   maxReplicas: 2
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         targetAverageUtilization: 60
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: call-service-user-ingress
  namespace: ruh-ai-dev
spec:
  ingressClassName: nginx
  rules:
  - host: call-service.ruh.ai
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: call-service-user-svc
            port:
              number: 80



