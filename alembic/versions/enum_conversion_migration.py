"""enum conversion migration

Revision ID: enum_conversion_migration
Revises: 
Create Date: 2023-11-10

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'enum_conversion_migration'
down_revision = None  # Adjust this based on your actual revision history
branch_labels = None
depends_on = None

def upgrade():
    # Add status column to batch_calls table
    op.add_column('batch_calls', 
                 sa.Column('status', sa.String(50), nullable=True, 
                           server_default='PENDING'))
    
    # Update existing data - CallDetail status field conversion
    # Here we would run SQL to convert any existing integer enum values in CallDetail table
    # This assumes your database already has the fields but they might contain integer values
    op.execute("""
    UPDATE call_details 
    SET status = 
        CASE status 
            WHEN '0' THEN 'PENDING'
            WHEN '1' THEN 'INITIATED'
            WHEN '2' THEN 'ONGOING'
            WHEN '3' THEN 'ENDED'
            WHEN '4' THEN 'FAILED'
            ELSE status
        END
    WHERE status ~ '^[0-4]$';
    """)
    
    # Update CallDetail type field conversion
    op.execute("""
    UPDATE call_details 
    SET type = 
        CASE type 
            WHEN '0' THEN 'INBOUND'
            WHEN '1' THEN 'OUTBOUND'
            ELSE type
        END
    WHERE type ~ '^[0-1]$';
    """)
    
    # Update CallDetail transfer_status field conversion
    op.execute("""
    UPDATE call_details 
    SET transfer_status = 
        CASE transfer_status 
            WHEN '0' THEN 'TRANSFER_FALSE'
            WHEN '1' THEN 'TRANSFER_TRUE'
            ELSE transfer_status
        END
    WHERE transfer_status ~ '^[0-1]$';
    """)

def downgrade():
    # Remove status column from batch_calls table
    op.drop_column('batch_calls', 'status')
    
    # Revert existing data - For a proper downgrade, convert back to integers
    # This would require SQL to convert string enums back to integers if needed 