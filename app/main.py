import os
from concurrent import futures

import grpc
import structlog

from app.core.config import settings
from app.grpc import call_pb2_grpc
from app.services import CallService

logger = structlog.get_logger()


def serve():
    # Create gRPC server
    server = grpc.server(futures.ThreadPoolExecutor(max_workers=10))

    # Add user service to server
    call_service = CallService()
    call_pb2_grpc.add_CallServiceServicer_to_server(call_service, server)

    # Get port from environment or use default
    port = os.getenv("PORT", "50058")
    server.add_insecure_port(f"[::]:{port}")

    # Start server
    server.start()
    logger.info(f"Call service started on port {port}")

    # Keep thread alive
    server.wait_for_termination()


if __name__ == "__main__":
    serve()
