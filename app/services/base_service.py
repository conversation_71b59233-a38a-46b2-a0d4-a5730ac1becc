import grpc
from livekit import api
from sqlalchemy.orm import Session

from app.core.config import settings
from app.db.session import SessionLocal


class BaseService:
    def __init__(self):
        self.livekit_api = None

    def get_db(self) -> Session:
        """
        Get database session. Fixed to return the session without closing it immediately.
        The session should be closed by the caller in a try/finally block.
        """
        return SessionLocal()

    async def initialize(self):
        """
        Async initialization method for LiveKit API
        """
        self.livekit_api = api.LiveKitAPI(
            settings.LIVEKIT_URL, 
            settings.LIVEKIT_API_KEY, 
            settings.LIVEKIT_API_SECRET
        )