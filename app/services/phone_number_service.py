from datetime import datetime
from uuid import UUI<PERSON>
from typing import List, Optional

import grpc
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.grpc import call_pb2
from app.models import PhoneNumber
from app.services.base_service import BaseService
from app.services.livekit_service import LiveKitService 


class PhoneNumberService(BaseService):
    def __init__(self):
        super().__init__()
        self.livekit_service = LiveKitService()  

    def _validate_create_agent_phone_line_request(self, request) -> str:
        """Validate the incoming request and return error message if invalid."""
        if not request.phone_number_id:
            return "Phone number ID is required"
        
        if not request.agent_id:
            return "Agent ID is required"
        
        # Validate agent_id format
        try:
            UUID(request.agent_id)
        except ValueError:
            return "Invalid agent ID format"
        
        # Validate phone numbers format if provided
        if request.allowed_incoming_numbers:
            for number in request.allowed_incoming_numbers:
                if not self._is_valid_phone_number(number):
                    return f"Invalid phone number format: {number}"
        
        return None


    async def _validate_and_fetch_phone_number(self, db, phone_number_id: str, context) -> PhoneNumber:
        """Validate and fetch phone number entity."""
        
        # Verify phone number exists
        phone_number = db.query(PhoneNumber).filter(
            PhoneNumber.id == phone_number_id
        ).first()
        
        if not phone_number:
            context.set_code(grpc.StatusCode.NOT_FOUND)
            context.set_details("Phone number not found")
            return None
        
        return phone_number
    
    def _is_valid_phone_number(self, phone_number: str) -> bool:
        """Validate phone number format."""
        # Implement your phone number validation logic
        # Basic example - adjust based on your requirements
        import re
        
        # Remove any spaces or special characters for validation
        cleaned_number = re.sub(r'[^\d+]', '', phone_number.strip())
        
        # E.164 format validation (international format)
        pattern = r'^\+?[1-9]\d{1,14}$'
        return bool(re.match(pattern, cleaned_number))


    def _log_error(self, message: str):
        """Log error message."""
        print(f"ERROR: {message}")
        # Consider using proper logging framework like logging.getLogger(__name__)


    def _log_info(self, message: str):
        """Log info message."""
        print(f"INFO: {message}")
    
    async def createAgentPhoneLine(
    self, request: call_pb2.CreateAgentPhoneLineRequest, context: grpc.ServicerContext
    ) -> call_pb2.CreateAgentPhoneLineResponse:
        """
        Creates/configures an agent phone line with allowlist following the core flow.
        
        Args:
            request (CreateAgentPhoneLineRequest): Contains phone_number_id, agent_id, etc.
            context (grpc.ServicerContext): gRPC service context
            
        Returns:
            CreateAgentPhoneLineResponse: Contains success status and created records
        """
        db = self.get_db()
        
        try:
            # Validate required fields
            validation_error = self._validate_create_agent_phone_line_request(request)
            if validation_error:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(validation_error)
                return call_pb2.CreateAgentPhoneLineResponse(
                    success=False,
                    message=validation_error
                )

            # Validate and fetch phone number
            phone_number = await self._validate_and_fetch_phone_number(
                db, request.phone_number_id, context
            )
            if not phone_number:
                return call_pb2.CreateAgentPhoneLineResponse(success=False)

            # Check if phone number is already assigned to a different agent
            if phone_number.agent_id and phone_number.agent_id != request.agent_id:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("Phone number is already assigned to another agent")
                return call_pb2.CreateAgentPhoneLineResponse(
                    success=False,
                    message="Phone number is already assigned to another agent"
                )

            # Check if LiveKit trunk already exists
            if phone_number.sip_inbound_trunk_id:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("LiveKit trunk already exists for this phone number")
                return call_pb2.CreateAgentPhoneLineResponse(
                    success=False,
                    message="LiveKit trunk already exists for this phone number"
                )

            # Generate trunk name if not provided
            trunk_name = request.trunk_name or f"Agent-{request.agent_id[:8]}-Trunk"
            print("trunk_name", trunk_name)
            
            # Create LiveKit trunk FIRST (before database transaction)
            livekit_trunk_id = await self.livekit_service.create_livekit_trunk(
                phone_number, trunk_name, request
            )
            
            # Begin database transaction AFTER LiveKit trunk creation
            try:
                # Update phone number with agent assignment, allowed numbers, and trunk info
                phone_number.agent_id = request.agent_id  # Store as string directly
                phone_number.allowed_incoming_numbers = list(request.allowed_incoming_numbers)
                phone_number.trunk_name = trunk_name
                phone_number.sip_inbound_trunk_id = livekit_trunk_id
                
                # Commit all database changes at once
                db.commit()
                db.refresh(phone_number)
                
                self._log_info(f"Updated phone number {phone_number.id} with agent {request.agent_id} and LiveKit trunk ID: {livekit_trunk_id}")

            except Exception as db_error:
                db.rollback()
                # If database transaction fails after LiveKit trunk is created,
                # you might want to clean up the LiveKit trunk here
                self._log_error(f"Database transaction failed, LiveKit trunk {livekit_trunk_id} may need cleanup")
                raise db_error

            # Prepare response
            return call_pb2.CreateAgentPhoneLineResponse(
                success=True,
                message="Agent phone line created successfully",
                data=call_pb2.AgentPhoneLine(
                    phone_id=str(phone_number.id),
                    phone_number=phone_number.phone_number,
                    agent_id=request.agent_id,
                    trunk_name=trunk_name,
                    allowed_numbers=list(request.allowed_incoming_numbers),
                    livekit_trunk_id=livekit_trunk_id or "",
                    created_at=phone_number.created_at.isoformat() if phone_number.created_at else "",
                    updated_at=phone_number.updated_at.isoformat() if phone_number.updated_at else ""
                )
            )

        except Exception as e:
            db.rollback()
            self._log_error(f"Error in createAgentPhoneLine: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error occurred")
            return call_pb2.CreateAgentPhoneLineResponse(
                success=False,
                message="Internal server error occurred"
            )
        finally:
            db.close()

    def getAgentPhoneLines(
    self, request: call_pb2.GetAgentPhoneLinesRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetAgentPhoneLinesResponse:
        """
        Retrieves a paginated list of phone numbers, optionally filtered by user_id.

        Args:
            request (GetAgentPhoneLinesRequest): Contains:
                - user_id (str, optional): UUID of the user to filter phone numbers
                - page (int): Page number for pagination (default: 1)
                - pageSize (int): Number of items per page (default: 10)
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetAgentPhoneLinesResponse: Contains:
                - data (List[AgentPhoneLine]): List of phone numbers with details
                - metadata (PaginationMetadata): Pagination information

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If user_id format is invalid
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Validate user_id format if provided
            if request.user_id:
                try:
                    # Just validate the format, don't convert to UUID object yet
                    UUID(request.user_id)
                except ValueError:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid user_id format")
                    return call_pb2.GetAgentPhoneLinesResponse()

            # Set pagination parameters
            page = request.page if request.page > 0 else 1
            page_size = request.pageSize if request.pageSize > 0 else 10
            offset = (page - 1) * page_size

            # Build base query - removed the join with Agent table
            base_query = db.query(PhoneNumber)

            # Apply user_id filter if provided
            if request.user_id:
                base_query = base_query.filter(PhoneNumber.user_id == request.user_id)

            # Get total count
            total_count = base_query.count()

            # Apply pagination and ordering
            phone_numbers = (
                base_query
                .order_by(PhoneNumber.created_at.desc())
                .offset(offset)
                .limit(page_size)
                .all()
            )

            # Convert to proto message
            phone_number_items = []
            for phone_number in phone_numbers:
                phone_number_items.append(
                    call_pb2.AgentPhoneLine(
                        phone_id=str(phone_number.id),
                        phone_number=phone_number.phone_number,
                        agent_id=phone_number.agent_id or "",  # Return agent_id as string
                        trunk_name=phone_number.trunk_name or "",
                        allowed_numbers=list(phone_number.allowed_incoming_numbers or []),
                        livekit_trunk_id=phone_number.sip_inbound_trunk_id or "",
                        created_at=phone_number.created_at.isoformat() if phone_number.created_at else "",
                        updated_at=phone_number.updated_at.isoformat() if phone_number.updated_at else ""
                    )
                )

            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0
            has_next_page = page < total_pages
            has_previous_page = page > 1

            return call_pb2.GetAgentPhoneLinesResponse(
                data=phone_number_items,
                metadata=call_pb2.PaginationMetadata(
                    total=total_count,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                )
            )

        except Exception as e:
            self._log_error(f"Error in getAgentPhoneLines: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error occurred")
            return call_pb2.GetAgentPhoneLinesResponse()
        finally:
            db.close()

    def addPhoneNumber(
        self, request: call_pb2.AddPhoneNumberRequest, context: grpc.ServicerContext
    ) -> call_pb2.AddPhoneNumberResponse:
        """
        Adds a new phone number record (legacy method - consider using createAgentPhoneLine instead).
        """
        db = self.get_db()

        try:
            # Validate required fields
            print(request.user_id)
            if not request.user_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("User ID cannot be empty")
                return call_pb2.AddPhoneNumberResponse(
                    success=False,
                    message="User ID is required",
                    phone_number=None
                )
            
            # Check if phone number already exists (if provided)
            if request.phone_number:
                existing_phone = db.query(PhoneNumber).filter(
                    PhoneNumber.phone_number == request.phone_number
                ).first()
                if existing_phone:
                    context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                    context.set_details("Phone number already exists")
                    return call_pb2.AddPhoneNumberResponse(
                        success=False,
                        message="Phone number already exists",
                        phone_number=None
                    )

            # Create new phone number record
            new_phone_number = PhoneNumber(
                user_id=request.user_id,
                # agent_id=UUID(request.agent_id) if request.agent_id else None,
                phone_number=request.phone_number or None,
                sip_inbound_trunk_id=request.sip_trunk_id or None
            )

            db.add(new_phone_number)
            db.commit()
            db.refresh(new_phone_number)

            # Return success response with created phone number details
            return call_pb2.AddPhoneNumberResponse(
                success=True,
                message="Phone number added successfully",
                phone_number=call_pb2.PhoneNumber(
                    id=str(new_phone_number.id),
                    user_id=new_phone_number.user_id,
                    agent_id=str(new_phone_number.agent_id) if new_phone_number.agent_id else "",
                    phone_number=new_phone_number.phone_number or "",
                    sip_trunk_id=new_phone_number.sip_inbound_trunk_id or "",
                    created_at=new_phone_number.created_at.isoformat(),
                    updated_at=new_phone_number.updated_at.isoformat(),
                )
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return call_pb2.AddPhoneNumberResponse(
                success=False,
                message=f"Internal server error: {str(e)}",
                phone_number=None
            )

        finally:
            db.close()

    def lookupAgentByTrunkNumber(
    self, request: call_pb2.LookupAgentByTrunkNumberRequest, context: grpc.ServicerContext
    ) -> call_pb2.LookupAgentByTrunkNumberResponse:
        """
        Lookup agent ID by trunk phone number.
        
        Args:
            request (LookupAgentByTrunkNumberRequest): Contains trunk_phone_number
            context (grpc.ServicerContext): gRPC service context
            
        Returns:
            LookupAgentByTrunkNumberResponse: Contains success status and agent_id
        """
        db = self.get_db()
        
        try:
            # Validate input
            if not request.trunk_phone_number:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Trunk phone number is required")
                return call_pb2.LookupAgentByTrunkNumberResponse(
                    success=False,
                    message="Trunk phone number is required",
                    agent_id="",
                    user_id=""
                )

            # Query phone number by phone_number field
            phone_number = db.query(PhoneNumber).filter(
                PhoneNumber.phone_number == request.trunk_phone_number
            ).first()
            
            if not phone_number:
                return call_pb2.LookupAgentByTrunkNumberResponse(
                    success=True,
                    message="No phone number found",
                    agent_id="",
                    user_id=""
                )
            
            # Return agent_id and user_id (can be None/empty)
            agent_id = phone_number.agent_id or ""
            user_id = phone_number.user_id or ""
            
            return call_pb2.LookupAgentByTrunkNumberResponse(
                success=True,
                message="Lookup completed successfully",
                agent_id=agent_id,
                user_id=user_id
            )

        except Exception as e:
            self._log_error(f"Error in lookupAgentByTrunkNumber: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error occurred")
            return call_pb2.LookupAgentByTrunkNumberResponse(
                success=False,
                message="Internal server error occurred",
                agent_id="",
                user_id=""
            )
        finally:
            db.close()

    async def updateAgentPhoneLine(
        self, request: call_pb2.UpdateAgentPhoneLineRequest, context: grpc.ServicerContext
    ) -> call_pb2.UpdateAgentPhoneLineResponse:
        """
        Updates an existing agent phone line configuration including trunk settings.
        
        Args:
            request (UpdateAgentPhoneLineRequest): Contains phone_number_id and optional update fields
            context (grpc.ServicerContext): gRPC service context
            
        Returns:
            UpdateAgentPhoneLineResponse: Contains success status and updated record
        """
        db = self.get_db()
        
        try:
            print("allowed list",request.allowed_incoming_numbers)
            # Validate required fields
            if not request.phone_number_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Phone number ID is required")
                return call_pb2.UpdateAgentPhoneLineResponse(
                    success=False,
                    message="Phone number ID is required"
                )

            # Validate agent_id format if provided
            if request.agent_id:
                try:
                    UUID(request.agent_id)
                except ValueError:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid agent ID format")
                    return call_pb2.UpdateAgentPhoneLineResponse(
                        success=False,
                        message="Invalid agent ID format"
                    )

            # Validate phone numbers format if provided
            if request.allowed_incoming_numbers:
                for number in request.allowed_incoming_numbers:
                    if not self._is_valid_phone_number(number):
                        context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                        context.set_details(f"Invalid phone number format: {number}")
                        return call_pb2.UpdateAgentPhoneLineResponse(
                            success=False,
                            message=f"Invalid phone number format: {number}"
                        )

            # Validate and fetch phone number
            phone_number = await self._validate_and_fetch_phone_number(
                db, request.phone_number_id, context
            )
            if not phone_number:
                return call_pb2.UpdateAgentPhoneLineResponse(success=False)

            # Check if phone number has an existing SIP trunk
            if not phone_number.sip_inbound_trunk_id:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("Phone number does not have an associated SIP trunk")
                return call_pb2.UpdateAgentPhoneLineResponse(
                    success=False,
                    message="Phone number does not have an associated SIP trunk"
                )

            # Update LiveKit trunk FIRST (before database transaction)
            try:
                print("#####",request.allowed_incoming_numbers)
                await self.livekit_service.update_livekit_trunk(
                    trunk_id=phone_number.sip_inbound_trunk_id,
                    trunk_name=request.trunk_name if request.trunk_name else None,
                    allowed_numbers=list(request.allowed_incoming_numbers) if request.allowed_incoming_numbers else None,
                    metadata=request.metadata if request.metadata else None,
                    krisp_enabled=request.krisp_enabled if hasattr(request, 'krisp_enabled') else None
                )
            except Exception as livekit_error:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Failed to update LiveKit trunk: {str(livekit_error)}")
                return call_pb2.UpdateAgentPhoneLineResponse(
                    success=False,
                    message=f"Failed to update LiveKit trunk: {str(livekit_error)}"
                )

            # Begin database transaction AFTER LiveKit trunk update
            try:
                # Update only provided fields
                if request.agent_id:
                    phone_number.agent_id = request.agent_id
                if request.trunk_name:
                    phone_number.trunk_name = request.trunk_name
                if request.allowed_incoming_numbers:
                    phone_number.allowed_incoming_numbers = list(request.allowed_incoming_numbers)
                
                db.commit()
                db.refresh(phone_number)
                
                self._log_info(f"Updated phone number {phone_number.id} and LiveKit trunk {phone_number.sip_inbound_trunk_id}")

            except Exception as db_error:
                db.rollback()
                self._log_error(f"Database update failed after LiveKit trunk update: {str(db_error)}")
                # Note: LiveKit trunk was updated but database failed - manual reconciliation may be needed
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Database update failed after trunk update")
                return call_pb2.UpdateAgentPhoneLineResponse(
                    success=False,
                    message="Database update failed after trunk update"
                )

            # Prepare response
            return call_pb2.UpdateAgentPhoneLineResponse(
                success=True,
                message="Agent phone line updated successfully",
                data=call_pb2.AgentPhoneLine(
                    phone_id=str(phone_number.id),
                    phone_number=phone_number.phone_number,
                    agent_id=phone_number.agent_id or "",
                    trunk_name=phone_number.trunk_name or "",
                    allowed_numbers=list(phone_number.allowed_incoming_numbers or []),
                    livekit_trunk_id=phone_number.sip_inbound_trunk_id or "",
                    created_at=phone_number.created_at.isoformat() if phone_number.created_at else "",
                    updated_at=phone_number.updated_at.isoformat() if phone_number.updated_at else ""
                )
            )

        except Exception as e:
            db.rollback()
            self._log_error(f"Error in updateAgentPhoneLine: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error occurred")
            return call_pb2.UpdateAgentPhoneLineResponse(
                success=False,
                message="Internal server error occurred"
            )
        finally:
            db.close()

    async def deleteAgentPhoneLine(
        self, request: call_pb2.DeleteAgentPhoneLineRequest, context: grpc.ServicerContext
    ) -> call_pb2.DeleteAgentPhoneLineResponse:
        """
        Deletes an agent phone line and associated SIP trunk.
        
        Args:
            request (DeleteAgentPhoneLineRequest): Contains phone_number_id and force_delete flag
            context (grpc.ServicerContext): gRPC service context
            
        Returns:
            DeleteAgentPhoneLineResponse: Contains success status and deleted trunk ID
        """
        db = self.get_db()
        
        try:
            # Validate required fields
            if not request.phone_number_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Phone number ID is required")
                return call_pb2.DeleteAgentPhoneLineResponse(
                    success=False,
                    message="Phone number ID is required"
                )

            # Validate and fetch phone number
            phone_number = await self._validate_and_fetch_phone_number(
                db, request.phone_number_id, context
            )
            if not phone_number:
                return call_pb2.DeleteAgentPhoneLineResponse(success=False)

            # Check if phone number has an associated SIP trunk
            if not phone_number.sip_inbound_trunk_id:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("Phone number does not have an associated SIP trunk")
                return call_pb2.DeleteAgentPhoneLineResponse(
                    success=False,
                    message="Phone number does not have an associated SIP trunk"
                )

            trunk_id_to_delete = phone_number.sip_inbound_trunk_id

            # TODO: Add check for trunk in use (active calls) if force_delete is False
            # This would require additional logic to check for active calls
            if not request.force_delete:
                # For now, we'll proceed with deletion
                # In a production system, you'd check for active calls here
                pass

            # Delete LiveKit trunk FIRST (before database transaction)
            try:
                await self.livekit_service.delete_livekit_trunk(trunk_id_to_delete)
            except Exception as livekit_error:
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Failed to delete LiveKit trunk: {str(livekit_error)}")
                return call_pb2.DeleteAgentPhoneLineResponse(
                    success=False,
                    message=f"Failed to delete LiveKit trunk: {str(livekit_error)}"
                )

            # Begin database transaction AFTER LiveKit trunk deletion
            try:
                # Clear trunk-related fields but preserve phone number record
                phone_number.sip_inbound_trunk_id = None
                phone_number.trunk_name = None
                phone_number.allowed_incoming_numbers = []
                # Clear agent_id when trunk deletion is successful
                phone_number.agent_id = None
                
                # Commit database changes
                db.commit()
                db.refresh(phone_number)
                
                self._log_info(f"Deleted LiveKit trunk {trunk_id_to_delete} and cleaned up phone number {phone_number.id}")

            except Exception as db_error:
                db.rollback()
                self._log_error(f"Database cleanup failed after LiveKit trunk deletion: {str(db_error)}")
                # Note: LiveKit trunk was deleted but database cleanup failed - manual reconciliation needed
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details("Database cleanup failed after trunk deletion")
                return call_pb2.DeleteAgentPhoneLineResponse(
                    success=False,
                    message="Database cleanup failed after trunk deletion"
                )

            # Prepare response
            return call_pb2.DeleteAgentPhoneLineResponse(
                success=True,
                message="Agent phone line deleted successfully",
                deleted_trunk_id=trunk_id_to_delete
            )

        except Exception as e:
            db.rollback()
            self._log_error(f"Error in deleteAgentPhoneLine: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error occurred")
            return call_pb2.DeleteAgentPhoneLineResponse(
                success=False,
                message="Internal server error occurred"
            )
        finally:
            db.close()

