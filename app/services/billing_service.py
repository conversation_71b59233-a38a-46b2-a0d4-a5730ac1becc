from datetime import datetime
from uuid import UUID

import grpc
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.grpc import call_pb2
from app.models import CallDetail
from app.services.base_service import BaseService


class BillingService(BaseService):
    def getUnbilledCalls(
        self, request: call_pb2.GetUnbilledCallsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetUnbilledCallsResponse:
        """
        Retrieves unbilled calls for a specific user, month, and year with pagination.

        Args:
            request: GetUnbilledCallsRequest containing user_id, month, year, and pagination parameters
            context: gRPC service context

        Returns:
            GetUnbilledCallsResponse: Response containing unbilled call data and pagination metadata
        """
        db: Session = self.get_db()
        try:
            # Set pagination parameters
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            # Build query for unbilled calls
            query = db.query(CallDetail).filter(
                CallDetail.user_id == request.user_id,
                CallDetail.month == request.month,
                CallDetail.year == request.year,
                CallDetail.billed == False,
            )

            # Get total count of unbilled calls
            total_unbilled_calls = query.count()

            # Apply pagination
            unbilled_calls = (
                query.order_by(CallDetail.created_at.desc()).offset(offset).limit(page_size).all()
            )

            # Construct response
            call_list = [
                call_pb2.CallDetail(
                    id=str(detail.id),
                    user_id=detail.user_id,
                    batch_call_id=str(detail.batch_call_id) if detail.batch_call_id else "",
                    call_duration=detail.call_duration or 0,
                    recording_url=str(detail.recording_url) or "",
                    transcription_url=str(detail.transcription_url) or "",
                    status=detail.status,
                    call_failing_reason=detail.call_failing_reason or "",
                    created_at=detail.created_at.isoformat(),
                    # Add new fields
                    type=detail.type or "",
                    from_phone=detail.from_phone or "",
                    to_phone=detail.to_phone or "",
                    transfer_status=detail.transfer_status or "",
                    additional_guidelines=detail.additional_guidelines or "",
                    call_metadata=detail.call_metadata or "",
                    month=detail.month or 0,
                    year=detail.year or 0,
                    billed=detail.billed,
                    invoice_id=detail.invoice_id or "",
                    agent_id=detail.agent_id or "",
                )
                for detail in unbilled_calls
            ]

            # Metadata Calculation
            total_pages = (
                (total_unbilled_calls + page_size - 1) // page_size
                if total_unbilled_calls > 0
                else 0
            )
            has_next_page = page < total_pages
            has_previous_page = page > 1

            return call_pb2.GetUnbilledCallsResponse(
                data=call_list,
                metadata=call_pb2.PaginationMetadata(
                    total=total_unbilled_calls,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error fetching unbilled calls: {str(e)}")
            return call_pb2.GetUnbilledCallsResponse()

        finally:
            db.close()

    def markCallsAsBilled(
        self, request: call_pb2.MarkCallsAsBilledRequest, context: grpc.ServicerContext
    ) -> call_pb2.MarkCallsAsBilledResponse:
        """
        Mark multiple calls as billed with an invoice ID.

        Args:
            request: MarkCallsAsBilledRequest containing call_ids and invoice_id
            context: gRPC service context

        Returns:
            MarkCallsAsBilledResponse: Response indicating success or failure with statistics
        """
        db: Session = self.get_db()
        try:
            # Initialize counters
            calls_processed = 0
            calls_billed = 0
            calls_failed = 0
            failed_call_ids = []

            # Process each call ID
            for call_id in request.call_ids:
                calls_processed += 1
                try:
                    # Convert string ID to UUID
                    call_uuid = UUID(call_id)

                    # Find the call detail
                    call_detail = db.query(CallDetail).filter(CallDetail.id == call_uuid).first()

                    if call_detail:
                        # Update the call detail
                        call_detail.billed = True
                        call_detail.invoice_id = request.invoice_id
                        calls_billed += 1
                    else:
                        # Call not found
                        calls_failed += 1
                        failed_call_ids.append(call_id)
                except ValueError:
                    # Invalid UUID format
                    calls_failed += 1
                    failed_call_ids.append(call_id)
                except Exception as e:
                    # Other errors
                    calls_failed += 1
                    failed_call_ids.append(call_id)

            # Commit changes
            db.commit()

            # Construct response
            return call_pb2.MarkCallsAsBilledResponse(
                success=True,
                message="Calls marked as billed successfully",
                calls_processed=calls_processed,
                calls_billed=calls_billed,
                calls_failed=calls_failed,
                failed_call_ids=failed_call_ids,
            )

        except SQLAlchemyError as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Database error: {str(e)}")
            return call_pb2.MarkCallsAsBilledResponse(
                success=False,
                message=f"Database error: {str(e)}",
                calls_processed=len(request.call_ids),
                calls_billed=0,
                calls_failed=len(request.call_ids),
                failed_call_ids=request.call_ids,
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error marking calls as billed: {str(e)}")
            return call_pb2.MarkCallsAsBilledResponse(
                success=False,
                message=f"Error marking calls as billed: {str(e)}",
                calls_processed=len(request.call_ids),
                calls_billed=0,
                calls_failed=len(request.call_ids),
                failed_call_ids=request.call_ids,
            )
        finally:
            db.close()
