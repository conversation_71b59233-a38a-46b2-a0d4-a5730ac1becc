import grpc
from sqlalchemy.orm import Session

from app.grpc import call_pb2
from app.models import AdminSettings
from app.services.base_service import BaseService


class AdminService(BaseService):
    def setCostPerSec(
        self, request: call_pb2.SetCostPerSecRequest, context
    ) -> call_pb2.AdminSettingsResponse:
        """
        Sets cost_per_sec. Creates an entry only if it doesn't exist.
        """
        db: Session = self.get_db()
        try:
            settings = db.query(AdminSettings).first()
            if settings:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("Settings already exist. Use update instead.")
                return call_pb2.AdminSettingsResponse(
                    success=False, message="Settings already exist"
                )

            new_setting = AdminSettings(cost_per_sec=request.cost_per_sec)
            db.add(new_setting)
            db.commit()
            return call_pb2.AdminSettingsResponse(
                success=True, message="Settings created successfully"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.AdminSettingsResponse(success=False, message="Internal server error")

        finally:
            db.close()

    def updateCostPerSec(self, request, context):
        """
        Updates the existing cost_per_sec.
        """
        db: Session = self.get_db()
        try:
            settings = db.query(AdminSettings).first()
            if not settings:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Settings not found. Create it first.")
                return call_pb2.AdminSettingsResponse(success=False, message="Settings not found")

            settings.cost_per_sec = request.cost_per_sec
            db.commit()
            return call_pb2.AdminSettingsResponse(success=True, message="Updated successfully")

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.AdminSettingsResponse(success=False, message="Internal server error")

        finally:
            db.close()
