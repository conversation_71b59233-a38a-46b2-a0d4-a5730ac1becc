from datetime import datetime
from uuid import UUID
import json

import grpc
from sqlalchemy.orm import Session

from app.grpc import call_pb2
from app.models import Agent, UserAgent
from app.services.base_service import BaseService


class AgentService(BaseService):
    def createAgent(
        self, request: call_pb2.CreateAgentRequest, context: grpc.ServicerContext
    ) -> call_pb2.CreateAgentResponse:
        """
        Creates a new agent in the system.

        Args:
            request (CreateAgentRequest): Contains:
                - title (str): Title of the agent
                - kind (str): Type/category of the agent
                - avatar_url (str): URL to agent's avatar image
                - system_prompt (str): Initial system prompt for the agent
                - voice_id (str): UUID of the voice to be used by the agent
                - metadata (str): JSON string containing CSV metadata configuration
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            CreateAgentResponse: Contains:
                - success (bool): True if creation successful, False otherwise
                - message (str): Description of the operation result

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If voice_id format or metadata JSON is invalid
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Convert voice_id string to UUID for foreign key
            try:
                voice_uuid = UUID(request.voice_id) if request.voice_id else None
            except ValueError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid voice_id format")
                return call_pb2.CreateAgentResponse(
                    success=False, message="Invalid voice_id format"
                )
            
            # Parse metadata JSON string to dict if provided
            metadata_dict = None
            print("request.metadata",request.metadata)
            if request.metadata:
                try:
                    metadata_dict = json.loads(request.metadata)
                except json.JSONDecodeError:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid metadata format")
                    return call_pb2.CreateAgentResponse(
                        success=False, message="Invalid metadata format"
                    )
            
            new_agent = Agent(
                title=request.title,
                kind=request.kind,
                avatar_url=request.avatar_url,
                system_prompt=request.system_prompt,
                voice_id=voice_uuid,
                csv_metadata=metadata_dict,
                created_at=datetime.utcnow(),
            )
            db.add(new_agent)
            db.commit()
            db.refresh(new_agent)
            
            return call_pb2.CreateAgentResponse(
                success=True,
                message="Agent created successfully",
            )
        
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.CreateAgentResponse()
        finally:
            db.close()

    def assignAgentToUser(self, request: call_pb2, context):
        """
        Assigns an agent to a user.

        Args:
            request (AssignAgentToUserRequest): Contains:
                - user_id (str): ID of the user to assign the agent to
                - agent_id (str): ID of the agent to be assigned
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            AssignAgentToUserResponse: Contains:
                - success (bool): True if assignment successful, False otherwise
                - message (str): Description of the operation result

        Raises:
            gRPC Status Codes:
                - NOT_FOUND: If agent doesn't exist
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            agent = db.query(Agent).filter(Agent.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent not found")
                return call_pb2.AssignAgentToUserResponse(success=False, message="Agent not found")

            existing_assignment = (
                db.query(UserAgent)
                .filter(
                    UserAgent.user_id == request.user_id, UserAgent.agent_id == request.agent_id
                )
                .first()
            )
            if existing_assignment:
                return call_pb2.AssignAgentToUserResponse(
                    success=False, message="Agent already assigned to user"
                )

            new_assignment = UserAgent(user_id=request.user_id, agent_id=request.agent_id)
            db.add(new_assignment)
            db.commit()

            return call_pb2.AssignAgentToUserResponse(
                success=True, message="Agent successfully assigned"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.AssignAgentToUserResponse(
                success=False, message="Internal server error"
            )

        finally:
            db.close()

    def getAgentDetails(
        self, request: call_pb2.GetAgentDetailsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetAgentDetailsResponse:
        """
        Retrieves detailed information about a specific agent.

        Args:
            request (GetAgentDetailsRequest): Contains:
                - agent_id (str): ID of the agent to retrieve details for
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetAgentDetailsResponse: Contains:
                - title (str): Agent's title
                - kind (str): Agent's type/category
                - avatar_url (str): URL to agent's avatar
                - system_prompt (str): Agent's system prompt
                - assigned_users (List[AgentAssignedUserId]): List of assigned user IDs
                - created_at (str): ISO formatted creation timestamp
                - voice_id (str): ID of the agent's voice

        Raises:
            gRPC Status Codes:
                - NOT_FOUND: If agent doesn't exist
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Fetch agent details
            agent = db.query(Agent).filter(Agent.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent not found")
                return call_pb2.GetAgentDetailsResponse()

            # Fetch assigned users
            user_agents = db.query(UserAgent).filter(UserAgent.agent_id == agent.id).all()
            assigned_users = [
                call_pb2.AgentAssignedUserId(user_id=str(ua.user_id)) for ua in user_agents
            ]

            # Construct & return response
            return call_pb2.GetAgentDetailsResponse(
                title=agent.title,
                kind=agent.kind,
                avatar_url=agent.avatar_url or "",
                system_prompt=agent.system_prompt,
                assigned_users=assigned_users,
                created_at=agent.created_at.isoformat(),
                voice_id=str(agent.voice_id),
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.GetAgentDetailsResponse()

        finally:
            db.close()

    def updateAssignedAgent(
        self, request: call_pb2.UpdateAssignedAgentRequest, context: grpc.ServicerContext
    ) -> call_pb2.UpdateAssignedAgentResponse:
        """
        Updates the assigned agent for a user.

        Args:
            request (UpdateAssignedAgentRequest): Contains:
                - user_id (str): ID of the user whose agent assignment needs updating
                - old_agent_id (str): ID of the currently assigned agent
                - new_agent_id (str): ID of the agent to be newly assigned
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            UpdateAssignedAgentResponse: Contains:
                - success (bool): True if update successful, False otherwise
                - message (str): Description of the operation result

        Raises:
            gRPC Status Codes:
                - NOT_FOUND: If old agent assignment or new agent not found
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Validate user-agent relationship exists
            user_agent = (
                db.query(UserAgent)
                .filter(
                    UserAgent.user_id == request.user_id, UserAgent.agent_id == request.old_agent_id
                )
                .first()
            )

            if not user_agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Old assigned agent not found for user")
                return call_pb2.UpdateAssignedAgentResponse(
                    success=False, message="Old assigned agent not found"
                )

            # Validate new agent exists
            new_agent = db.query(Agent).filter(Agent.id == request.new_agent_id).first()
            if not new_agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("New agent not found")
                return call_pb2.UpdateAssignedAgentResponse(
                    success=False, message="New agent not found"
                )

            # Update assignment
            user_agent.agent_id = request.new_agent_id
            db.commit()

            return call_pb2.UpdateAssignedAgentResponse(
                success=True, message="Agent assignment updated successfully"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.UpdateAssignedAgentResponse(
                success=False, message="Internal server error"
            )

        finally:
            db.close()

    def deleteAssignedAgent(
        self, request: call_pb2.DeleteAssignedAgentRequest, context: grpc.ServicerContext
    ) -> call_pb2.DeleteAssignedAgentResponse:
        """
        Removes an assigned agent from a user.

        Args:
            request (DeleteAssignedAgentRequest): Contains:
                - user_id (str): ID of the user
                - agent_id (str): ID of the agent to unassign
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            DeleteAssignedAgentResponse: Contains:
                - success (bool): True if deletion successful, False otherwise
                - message (str): Description of the operation result

        Raises:
            gRPC Status Codes:
                - NOT_FOUND: If assignment doesn't exist
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Check if the assignment exists
            user_agent = (
                db.query(UserAgent)
                .filter(
                    UserAgent.user_id == request.user_id, UserAgent.agent_id == request.agent_id
                )
                .first()
            )

            if not user_agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent assignment not found for user")
                return call_pb2.DeleteAssignedAgentResponse(
                    success=False, message="Agent assignment not found"
                )

            # Delete the assignment
            db.delete(user_agent)
            db.commit()

            return call_pb2.DeleteAssignedAgentResponse(
                success=True, message="Agent assignment deleted successfully"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.DeleteAssignedAgentResponse(
                success=False, message="Internal server error"
            )

        finally:
            db.close()

    def getAssignedAgents(
        self, request: call_pb2.GetAssignedAgentsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetAssignedAgentsResponse:
        """
        Retrieve agents assigned to a specific user with pagination.

        Args:
            request (GetAssignedAgentsRequest): Contains:
                - user_id (str): ID of the user to get assigned agents for
                - page (int): Page number for pagination (default: 1)
                - page_size (int): Number of items per page (default: 10)
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetAssignedAgentsResponse: Contains:
                - data (List[AgentAssignment]): List of assigned agents with details:
                    - agent_id (str): ID of the agent
                    - title (str): Agent's title
                    - kind (str): Agent's type
                    - assigned_at (str): ISO formatted assignment timestamp
                    - voice_id (str): ID of the agent's voice
                    - metadata (str): JSON string of agent's CSV metadata
                - metadata (PaginationMetadata): Pagination information:
                    - total (int): Total number of assignments
                    - totalPages (int): Total number of pages
                    - currentPage (int): Current page number
                    - pageSize (int): Items per page
                    - hasNextPage (bool): Whether there's a next page
                    - hasPreviousPage (bool): Whether there's a previous page

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If user_id is empty
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            if not request.user_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("User ID cannot be empty")
                return call_pb2.GetAssignedAgentsResponse()

            # Set pagination parameters
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            # Base query for counting total results
            base_query = (
                db.query(UserAgent).join(Agent).filter(UserAgent.user_id == request.user_id)
            )

            # Get total count
            total_count = base_query.count()

            if total_count == 0:
                # Return empty response with pagination metadata
                return call_pb2.GetAssignedAgentsResponse(
                    data=[],
                    metadata=call_pb2.PaginationMetadata(
                        total=0,
                        totalPages=0,
                        currentPage=page,
                        pageSize=page_size,
                        hasNextPage=False,
                        hasPreviousPage=False,
                    ),
                )

            # Apply pagination
            user_agent_assignments = base_query.offset(offset).limit(page_size).all()

            # Build response data
            response_agents = []
            for user_agent in user_agent_assignments:
                agent = user_agent.agent

                agent_assignment = call_pb2.AgentAssignment(
                    agent_id=str(agent.id) if agent.id else "",
                    title=agent.title or "",
                    kind=agent.kind,
                    assigned_at=user_agent.assigned_at.isoformat(),
                    voice_id=str(agent.voice_id),
                    metadata=json.dumps(agent.csv_metadata) if agent.csv_metadata else "",
                )
                response_agents.append(agent_assignment)

            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0
            has_next_page = page < total_pages
            has_previous_page = page > 1

            # Return response with data and metadata
            return call_pb2.GetAssignedAgentsResponse(
                data=response_agents,  # Changed from 'agents' to 'data' to match updated proto
                metadata=call_pb2.PaginationMetadata(
                    total=total_count,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return call_pb2.GetAssignedAgentsResponse()

        finally:
            db.close()

    def getAllAgents(
        self, request: call_pb2.GetAllAgentsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetAllAgentsResponse:
        """
        Retrieve a paginated list of all agents in the system.

        Args:
            request (GetAllAgentsRequest): Contains:
                - page (int): Page number for pagination (default: 1)
                - page_size (int): Number of items per page (default: 10)
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetAllAgentsResponse: Contains:
                - data (List[AgentData]): List of agents with details:
                    - agent_id (str): ID of the agent
                    - title (str): Agent's title
                    - kind (str): Agent's type
                    - avatar_url (str): URL to agent's avatar
                    - system_prompt (str): Agent's system prompt
                    - voice_id (str): ID of the agent's voice
                    - created_at (str): ISO formatted creation timestamp
                - metadata (PaginationMetadata): Pagination information:
                    - total (int): Total number of agents
                    - totalPages (int): Total number of pages
                    - currentPage (int): Current page number
                    - pageSize (int): Items per page
                    - hasNextPage (bool): Whether there's a next page
                    - hasPreviousPage (bool): Whether there's a previous page

        Raises:
            gRPC Status Codes:
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Set pagination parameters
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            # Base query for counting total agents
            base_query = db.query(Agent)

            # Get total count
            total_count = base_query.count()

            if total_count == 0:
                # Return empty response with pagination metadata
                return call_pb2.GetAllAgentsResponse(
                    data=[],
                    metadata=call_pb2.PaginationMetadata(
                        total=0,
                        totalPages=0,
                        currentPage=page,
                        pageSize=page_size,
                        hasNextPage=False,
                        hasPreviousPage=False,
                    ),
                )

            # Apply pagination
            agents = (
                base_query.order_by(Agent.created_at.desc()).offset(offset).limit(page_size).all()
            )

            # Build response data
            agent_data_list = []
            for agent in agents:
                agent_data = call_pb2.AgentData(
                    agent_id=str(agent.id),
                    title=agent.title,
                    kind=agent.kind,
                    avatar_url=agent.avatar_url or "",
                    system_prompt=agent.system_prompt,
                    voice_id=str(agent.voice_id),
                    created_at=agent.created_at.isoformat(),
                )
                agent_data_list.append(agent_data)

            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0
            has_next_page = page < total_pages
            has_previous_page = page > 1

            # Return response with data and metadata
            return call_pb2.GetAllAgentsResponse(
                data=agent_data_list,
                metadata=call_pb2.PaginationMetadata(
                    total=total_count,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving agents: {str(e)}")
            return call_pb2.GetAllAgentsResponse()

        finally:
            db.close()

    def getDefaultAgents(
        self, request: call_pb2.GetDefaultAgentsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetDefaultAgentsResponse:
        """
        Retrieves all agents with kind 'default'.

        Args:
            request (GetDefaultAgentsRequest): Empty request
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetDefaultAgentsResponse: Contains:
                - agents (List[DefaultAgent]): List of default agents with:
                    - id (str): Agent's ID
                    - avatar_url (str): URL to agent's avatar
                    - name (str): Agent's title/name
        """
        db = self.get_db()
        try:
            # Query for default agents
            default_agents = db.query(Agent).filter(Agent.kind == "default").all()
            
            # Convert to response format
            agent_list = [
                call_pb2.DefaultAgent(
                    id=str(agent.id),
                    avatar_url=agent.avatar_url or "",
                    name=agent.title
                )
                for agent in default_agents
            ]
            
            return call_pb2.GetDefaultAgentsResponse(agents=agent_list)

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving default agents: {str(e)}")
            return call_pb2.GetDefaultAgentsResponse()

        finally:
            db.close()
