import asyncio
import grpc

from app.grpc import call_pb2, call_pb2_grpc
from app.services.admin_service import AdminService
from app.services.agent_service import AgentService
from app.services.base_service import BaseService
from app.services.batch_call_service import BatchCallService
from app.services.billing_service import BillingService
from app.services.call_detail_service import CallDetailService
from app.services.contact_service import ContactService
from app.services.outbound_service import OutboundService
from app.services.voice_service import VoiceService
from app.services.phone_number_service import PhoneNumberService


class CallService(call_pb2_grpc.CallServiceServicer, BaseService):
    def __init__(self):
        super().__init__()
        self.admin_service = AdminService()
        self.agent_service = AgentService()
        self.batch_call_service = BatchCallService()
        self.call_detail_service = CallDetailService()
        self.billing_service = BillingService()
        self.contact_service = ContactService()
        self.outbound_service = OutboundService()
        self.voice_service = VoiceService()
        self.phone_number_service = PhoneNumberService()
        self.loop = asyncio.get_event_loop()

    # AdminSettings methods
    def setCostPerSec(self, request, context):
        return self.admin_service.setCostPerSec(request, context)

    def updateCostPerSec(self, request, context):
        return self.admin_service.updateCostPerSec(request, context)

    # Agent methods
    def createAgent(self, request, context):
        return self.agent_service.createAgent(request, context)

    def assignAgentToUser(self, request, context):
        return self.agent_service.assignAgentToUser(request, context)

    def getAgentDetails(self, request, context):
        return self.agent_service.getAgentDetails(request, context)

    def updateAssignedAgent(self, request, context):
        return self.agent_service.updateAssignedAgent(request, context)

    def deleteAssignedAgent(self, request, context):
        return self.agent_service.deleteAssignedAgent(request, context)

    def getAssignedAgents(self, request, context):
        return self.agent_service.getAssignedAgents(request, context)

    def getAllAgents(self, request, context):
        return self.agent_service.getAllAgents(request, context)

    def getDefaultAgents(self, request, context):
        return self.agent_service.getDefaultAgents(request, context)

    # BatchCall methods
    def getAllBatchCalls(self, request, context):
        return self.batch_call_service.getAllBatchCalls(request, context)

    def createBatchCall(self, request, context):
        return self.batch_call_service.createBatchCall(request, context)

    def getBatchCalls(self, request, context):
        return self.batch_call_service.getBatchCalls(request, context)

    def updateBatchCall(self, request, context):
        return self.batch_call_service.updateBatchCall(request, context)

    # CallDetail methods
    def getAllCalls(self, request, context):
        return self.call_detail_service.getAllCalls(request, context)

    def storeCallDetails(self, request, context):
        return self.call_detail_service.storeCallDetails(request, context)

    def getCallDetails(self, request, context):
        return self.call_detail_service.getCallDetails(request, context)

    def updateCallDetails(self, request, context):
        return self.call_detail_service.updateCallDetails(request, context)

    def getUserCalls(self, request, context):
        return self.call_detail_service.getUserCalls(request, context)

    # Contact methods
    def uploadContacts(self, request, context):
        return self.contact_service.uploadContacts(request, context)

    def getContacts(self, request, context):
        return self.contact_service.getContacts(request, context)
    
    def getContactLists(self, request, context):
        return self.contact_service.getContactLists(request, context)

    def getContactById(self, request, context):
        return self.contact_service.getContactById(request, context)

    def getAllContacts(self, request, context):
        return self.contact_service.getAllContacts(request, context)

    # Outbound methods
    def makeOutboundCall(self, request, context):
        return self.loop.run_until_complete(
            self.outbound_service.makeOutboundCall(request, context)
        )

    def nodeOutboundCall(self, request, context):
        return self.loop.run_until_complete(
            self.outbound_service.nodeoutboundCall(request, context)
        )

    # Voice methods
    def createVoice(self, request, context):
        return self.voice_service.createVoice(request, context)

    def getVoiceById(self, request, context):
        return self.voice_service.getVoiceById(request, context)

    def getVoiceByVoiceId(self, request, context):
        return self.voice_service.getVoiceByVoiceId(request, context)

    def listVoices(self, request, context):
        return self.voice_service.listVoices(request, context)

    def updateVoice(self, request, context):
        return self.voice_service.updateVoice(request, context)

    def deleteVoice(self, request, context):
        return self.voice_service.deleteVoice(request, context)

    # Billing methods
    def getUnbilledCalls(self, request, context):
        return self.billing_service.getUnbilledCalls(request, context)

    def markCallsAsBilled(self, request, context):
        return self.billing_service.markCallsAsBilled(request, context)
    
    #phone_numner methods    
    def getPhoneNumber(self, request, context):
        return self.phone_number_service.getPhoneNumber(request, context)
    
    def addPhoneNumber(self, request, context):
        return self.phone_number_service.addPhoneNumber(request, context)
    
    def createAgentPhoneLine(self, request, context):
        return self.loop.run_until_complete(
            self.phone_number_service.createAgentPhoneLine(request, context)
        )
    
    def getAgentPhoneLines(self, request, context):
        return self.phone_number_service.getAgentPhoneLines(request, context)
    
    def updateAgentPhoneLine(self, request, context):
        return self.loop.run_until_complete(
            self.phone_number_service.updateAgentPhoneLine(request, context)
        )
    
    def deleteAgentPhoneLine(self, request, context):
        return self.loop.run_until_complete(
            self.phone_number_service.deleteAgentPhoneLine(request, context)
        )
    
    def lookupAgentByTrunkNumber(self, request, context):
        return self.phone_number_service.lookupAgentByTrunkNumber(request, context)
    
    def checkRemainingAttempts(self, request, context):
        return self.outbound_service.checkRemainingAttempts(request, context)
        

    # Override initialize to initialize all dependent services
    def initialize(self):
        self.loop.run_until_complete(self.outbound_service.initialize())
