from datetime import datetime
from uuid import UUID

import grpc
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.grpc import call_pb2
from app.models import BatchCall
from app.services.base_service import BaseService


class BatchCallService(BaseService):

    def getAllBatchCalls(
        self, request: call_pb2.GetAllBatchCallsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetBatchCallsResponse:
        """
        Args:
            request (GetAllBatchCallsRequest): Contains:
                - page (int): Page number for pagination
                - pageSize (int): Number of items per page
            context (grpc.ServicerContext): gRPC service context

        Returns:
            GetBatchCallsResponse: Contains:
                - data (List[BatchCall]): List of batch calls for the current page
                - metadata (PaginationMetadata): Pagination information including:
                    - total (int): Total number of records
                    - totalPages (int): Total number of pages
                    - currentPage (int): Current page number
                    - pageSize (int): Number of items per page
                    - hasNextPage (bool): Whether there is a next page
                    - hasPreviousPage (bool): Whether there is a previous page
        """
        db: Session = self.get_db()
        try:
            page = request.page if request.page > 0 else 1
            page_size = request.pageSize if request.pageSize > 0 else 10

            # Filter by user_id
            user_batch_calls_query = db.query(BatchCall).filter(BatchCall.status == "pending")

            # Get total count after filtering
            total = user_batch_calls_query.count()

            # Apply pagination
            batch_calls_query = (
                user_batch_calls_query.offset((page - 1) * page_size).limit(page_size).all()
            )
            total_pages = (total + page_size - 1) // page_size

            # Calculate pagination metadata
            has_next_page = page < total_pages
            has_previous_page = page > 1

            batch_calls = []
            for bc in batch_calls_query:
                batch_calls.append(
                    call_pb2.BatchCall(
                        id=str(bc.id),  
                        user_id=bc.user_id,
                        contact_id=str(bc.contact_id),
                        title=bc.title,
                        description=bc.description if bc.description else "",
                        additional_guidelines=(
                            bc.additional_guidelines if bc.additional_guidelines else ""
                        ),
                        transfer_to=bc.transfer_to if bc.transfer_to else "",
                        service_provider=bc.service_provider if bc.service_provider else "",
                        schedule_time=bc.schedule_time.isoformat() if bc.schedule_time else "",
                        created_at=bc.created_at.isoformat(),
                        status=bc.status,
                    )
                )

            # Return response with updated structure
            return call_pb2.GetBatchCallsResponse(
                data=batch_calls, 
                metadata=call_pb2.PaginationMetadata(
                    total=total,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )
        
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error fetching batch calls: {str(e)}")
            return call_pb2.GetAllBatchCallsResponse()

        finally:
            db.close()

    def createBatchCall(
        self, request: call_pb2.CreateBatchCallRequest, context: grpc.ServicerContext
    ) -> call_pb2.CreateBatchCallResponse:
        """
        Creates a new batch call.

        Args:
            request (CreateBatchCallRequest): Contains:
                - user_id (str): ID of the user creating the batch call
                - contact_id (str): ID of the contact associated with the batch call
                - title (str): Title of the batch call
                - description (str): Description of the batch call
                - additional_guidelines (str): Additional guidelines for the batch call
                - transfer_to (str): Transfer destination
                - service_provider (str): Service provider information
                - start_now (bool): Whether to start the batch call immediately
                - schedule_time (Timestamp): Scheduled time for the batch call
                - status (str): Status of the batch call (defaults to "pending")
            context (grpc.ServicerContext): gRPC service context

        Returns:
            CreateBatchCallResponse: Contains:
                - success (bool): Whether the creation was successful
                - message (str): Success or error message
        """
        db: Session = self.get_db()
        try:
            if request.start_now:
                scheduled_dt = datetime.utcnow()
            else:
                scheduled_dt = None
                if request.schedule_time.seconds or request.schedule_time.nanos:
                    try:
                        scheduled_dt = request.schedule_time.ToDatetime()
                    except Exception as e:
                        context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                        context.set_details(f"Invalid schedule_time format: {e}")
                        return call_pb2.CreateBatchCallResponse(
                            success=False, message="Invalid schedule_time format."
                        )

            new_batch_call = BatchCall(
                user_id=request.user_id,
                contact_id=UUID(request.contact_id),
                title=request.title,
                description=request.description,
                additional_guidelines=request.additional_guidelines,
                transfer_to=request.transfer_to,
                service_provider=request.service_provider,
                schedule_time=scheduled_dt,
                created_at=datetime.utcnow(),
                status=request.status if request.status else "pending",
            )
            db.add(new_batch_call)
            db.commit()
            db.refresh(new_batch_call)

            return call_pb2.CreateBatchCallResponse(
                success=True,
                message="Batch call created successfully",
            )
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.CreateBatchCallResponse(success=False, message=str(e))
        finally:
            db.close()

    def getBatchCalls(
        self, request: call_pb2.GetBatchCallsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetBatchCallsResponse:
        """
        Retrieves batch calls for a specific user with pagination.

        Args:
            request (GetBatchCallsRequest): Contains:
                - user_id (str): ID of the user whose batch calls to retrieve
                - page (int): Page number for pagination
                - pageSize (int): Number of items per page
            context (grpc.ServicerContext): gRPC service context

        Returns:
            GetBatchCallsResponse: Contains:
                - data (List[BatchCall]): List of batch calls for the current page
                - metadata (PaginationMetadata): Pagination information including:
                    - total (int): Total number of records
                    - totalPages (int): Total number of pages
                    - currentPage (int): Current page number
                    - pageSize (int): Number of items per page
                    - hasNextPage (bool): Whether there is a next page
                    - hasPreviousPage (bool): Whether there is a previous page
        """
        db = self.get_db()
        try:
            user_id = request.user_id
            page = request.page if request.page > 0 else 1
            page_size = request.pageSize if request.pageSize > 0 else 10

            # Filter by user_id
            user_batch_calls_query = db.query(BatchCall).filter(BatchCall.user_id == user_id)

            # Get total count after filtering
            total = user_batch_calls_query.count()

            # Apply pagination
            batch_calls_query = (
                user_batch_calls_query.offset((page - 1) * page_size).limit(page_size).all()
            )
            total_pages = (total + page_size - 1) // page_size

            # Calculate pagination metadata
            has_next_page = page < total_pages
            has_previous_page = page > 1

            batch_calls = []
            for bc in batch_calls_query:
                batch_calls.append(
                    call_pb2.BatchCall(
                        id=str(bc.id),  
                        user_id=bc.user_id,
                        contact_id=str(bc.contact_id),
                        title=bc.title,
                        description=bc.description if bc.description else "",
                        additional_guidelines=(
                            bc.additional_guidelines if bc.additional_guidelines else ""
                        ),
                        transfer_to=bc.transfer_to if bc.transfer_to else "",
                        service_provider=bc.service_provider if bc.service_provider else "",
                        schedule_time=bc.schedule_time.isoformat() if bc.schedule_time else "",
                        created_at=bc.created_at.isoformat(),
                        status=bc.status,
                    )
                )

            return call_pb2.GetBatchCallsResponse(
                data=batch_calls,  # Changed from batch_calls to data
                metadata=call_pb2.PaginationMetadata(
                    total=total,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.GetBatchCallsResponse()
        finally:
            db.close()

    def updateBatchCall(
        self, request: call_pb2.UpdateBatchCallRequest, context: grpc.ServicerContext
    ) -> call_pb2.UpdateBatchCallResponse:
        """
        Update an existing batch call.

        Args:
            request (UpdateBatchCallRequest): Request containing batch_call_id and fields to update
            context (grpc.ServicerContext): gRPC service context

        Returns:
            UpdateBatchCallResponse: Response indicating success or failure
        """
        db = self.get_db()
        try:
            # Convert batch_call_id to UUID and find the batch call
            try:
                batch_call_id = UUID(request.batch_call_id)
            except ValueError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid batch_call_id format")
                return call_pb2.UpdateBatchCallResponse(
                    success=False, message="Invalid batch_call_id format"
                )

            # Find the batch call
            batch_call = db.query(BatchCall).filter(BatchCall.id == batch_call_id).first()
            if not batch_call:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Batch call not found")
                return call_pb2.UpdateBatchCallResponse(
                    success=False, message="Batch call not found"
                )

            # Update fields if provided in request
            if request.HasField("title"):
                batch_call.title = request.title
            if request.HasField("description"):
                batch_call.description = request.description
            if request.HasField("additional_guidelines"):
                batch_call.additional_guidelines = request.additional_guidelines
            if request.HasField("transfer_to"):
                batch_call.transfer_to = request.transfer_to
            if request.HasField("service_provider"):
                batch_call.service_provider = request.service_provider
            if request.HasField("status"):
                batch_call.status = request.status

            # Handle schedule_time if provided
            if request.HasField("schedule_time"):
                try:
                    batch_call.schedule_time = request.schedule_time.ToDatetime()
                except Exception as e:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid schedule_time format: {e}")
                    return call_pb2.UpdateBatchCallResponse(
                        success=False, message="Invalid schedule_time format"
                    )

            # Commit changes
            db.commit()
            db.refresh(batch_call)

            return call_pb2.UpdateBatchCallResponse(
                success=True, message="Batch call updated successfully"
            )

        except SQLAlchemyError as db_error:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Database error: {str(db_error)}")
            return call_pb2.UpdateBatchCallResponse(
                success=False, message="Database error occurred"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return call_pb2.UpdateBatchCallResponse(success=False, message="Internal server error")

        finally:
            db.close()
