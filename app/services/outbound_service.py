import json
import uuid
from datetime import datetime

import grpc
from livekit.protocol.sip import CreateSIPParticipantRequest
from livekit import api

from app.grpc import call_pb2
from app.services.base_service import BaseService
from app.models.demo_call import DemoCall
from app.models.agent import Agent
from app.models.call_detail import CallDetail


class OutboundService(BaseService):
    MAX_DEMO_ATTEMPTS = 3

    async def get_sip_trunk(self):
        """Get the first available SIP trunk ID"""
        if self.livekit_api is None:
            await self.initialize()

        trunk_request = api.ListSIPOutboundTrunkRequest()
        trunk_response = await self.livekit_api.sip.list_sip_outbound_trunk(trunk_request)
        if not trunk_response.items:
            raise ValueError("No SIP outbound trunks configured in LiveKit.")
        trunk_id = trunk_response.items[0].sip_trunk_id
        return trunk_id

    async def makeOutboundCall(
        self, request: call_pb2.OutboundCallRequest, context: grpc.ServicerContext
    ) -> call_pb2.OutboundCallResponse:
        if self.livekit_api is None:
            await self.initialize()

        try:
            db = self.get_db()
            
            # Get agent from database
            agent = db.query(Agent).filter(Agent.id == request.agent_id).first()
            if not agent:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Agent not found")
                return call_pb2.OutboundCallResponse(
                    success=False,
                    message="Agent not found"
                )
            
            # Get or create demo call record
            demo_call = db.query(DemoCall).filter(
                DemoCall.phone_number == request.phone_number
            ).first()

            if demo_call:
                if demo_call.attempts_used >= self.MAX_DEMO_ATTEMPTS:
                    return call_pb2.OutboundCallResponse(
                        success=False,
                        message=f"Free demo call limit exceeded. Maximum {self.MAX_DEMO_ATTEMPTS} demo calls allowed per phone number."
                    )
                # Increment attempts
                demo_call.attempts_used += 1
                demo_call.updated_at = datetime.utcnow()
            else:
                # Create new record
                demo_call = DemoCall(
                    id=uuid.uuid4(),
                    phone_number=request.phone_number,
                    user_name=request.user_name,
                    attempts_used=1
                )
                db.add(demo_call)

            # Create room using phone number as room name
            try:
                metadata = {
                    "system_prompt": agent.system_prompt,
                    "name": request.user_name,
                    "call_id": str(demo_call.id),
                    "phone_number": request.phone_number,
                }
                await self.livekit_api.room.create_room(
                    api.CreateRoomRequest(
                        name=request.phone_number,
                        metadata=json.dumps(metadata)
                    )
            )
            except Exception as e:
                # If room already exists, update metadata
                if 'room already exists' in str(e).lower():
                    await self.livekit_api.room.update_room_metadata(
                        api.UpdateRoomMetadataRequest(
                            room=request.phone_number,
                            metadata=json.dumps(metadata)
                        )
                    )
                    return True
                else:
                    raise

            trunk_id = await self.get_sip_trunk()
            print("trunk_id",trunk_id)


            # Create SIP participant
            sipRequest = CreateSIPParticipantRequest(
                sip_trunk_id=trunk_id,
                sip_call_to=request.phone_number,
                room_name=request.phone_number,  # Using phone number as room name
                participant_identity=f"{request.phone_number}-{demo_call.id}",
                participant_name=request.phone_number,
                krisp_enabled=True,
            )

            await self.livekit_api.sip.create_sip_participant(sipRequest)

            db.commit() 

            return call_pb2.OutboundCallResponse(
                success=True,
                message=f"Demo call initiated successfully. {self.MAX_DEMO_ATTEMPTS - demo_call.attempts_used} attempts remaining."
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to initiate call: {str(e)}")
            return call_pb2.OutboundCallResponse(
                success=False,
                message=f"Failed to initiate call: {str(e)}"
            )
        
    def checkRemainingAttempts(
    self, request: call_pb2.CheckAttemptsRequest, context: grpc.ServicerContext
    ) -> call_pb2.CheckAttemptsResponse:
        """
        Check remaining demo call attempts for a phone number and increment attempt count
        """
        try:
            print(request.phone_number)
            db = self.get_db()
            
            # Get demo call record by phone number
            demo_call = db.query(DemoCall).filter(
                DemoCall.phone_number == request.phone_number
            ).first()

            if demo_call:
                # Record exists - increment attempts_used by 1
                demo_call.attempts_used += 1
                db.commit()
                
                attempts_used = demo_call.attempts_used
                attempts_remaining = max(0, 3 - attempts_used)
                
                # Check if limit exceeded after incrementing (max 3 attempts)
                if attempts_used > 3:
                    return call_pb2.CheckAttemptsResponse(
                        success=True,
                        attempts_remaining=0,
                        attempts_used=attempts_used,
                        max_attempts=3,
                        limit_exceeded=True,
                        message=f"Free demo call limit exceeded. Maximum 3 demo calls allowed per phone number."
                    )
                else:
                    return call_pb2.CheckAttemptsResponse(
                        success=True,
                        attempts_remaining=attempts_remaining,
                        attempts_used=attempts_used,
                        max_attempts=3,
                        limit_exceeded=False,
                        message=f"{attempts_remaining} demo call attempts remaining for this phone number."
                    )
            else:
                # No record found - create new record with attempts_used = 1
                demo_call = DemoCall(
                    id=uuid.uuid4(),
                    phone_number=request.phone_number,
                    user_name=f"user_{request.phone_number}",
                    attempts_used=1  # Start with 1 attempt used
                )
                db.add(demo_call)
                db.commit()
                
                attempts_remaining = max(0, 3 - 1)
                
                # Check if limit exceeded on first attempt (max 3 attempts)
                if 1 > 3:
                    return call_pb2.CheckAttemptsResponse(
                        success=True,
                        attempts_remaining=0,
                        attempts_used=1,
                        max_attempts=3,
                        limit_exceeded=True,
                        message=f"Free demo call limit exceeded. Maximum 3 demo calls allowed per phone number."
                    )
                else:
                    return call_pb2.CheckAttemptsResponse(
                        success=True,
                        attempts_remaining=attempts_remaining,
                        attempts_used=1,
                        max_attempts=3,
                        limit_exceeded=False,
                        message=f"{attempts_remaining} demo call attempts remaining for this phone number."
                    )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to check attempts: {str(e)}")
            return call_pb2.CheckAttemptsResponse(
                success=False,
                attempts_remaining=0,
                attempts_used=0,
                max_attempts=3,
                limit_exceeded=False,
                message=f"Failed to check attempts: {str(e)}"
            )

    def fetch_agent_by_id(self, agent_id: str):
        """
        Fetch agent by ID - placeholder for future implementation
        
        Args:
            agent_id (str): The agent ID to fetch
            
        Returns:
            None: Currently returns None, to be implemented in future
        """
        # TODO: Implement agent fetching logic in future
        pass

    async def nodeoutboundCall(
        self, request: call_pb2.NodeOutboundCallRequest, context: grpc.ServicerContext
    ) -> call_pb2.NodeOutboundCallResponse:
        """
        Node outbound call method using CallDetail model with enhanced metadata support
        """
        if self.livekit_api is None:
            await self.initialize()

        try:
            db = self.get_db()
            
            # Call placeholder agent fetching function if agent_id provided
            if request.agent_id:
                self.fetch_agent_by_id(request.agent_id)
            
            # Create new CallDetail record
            call_detail = CallDetail(
                id=uuid.uuid4(),
                user_id=request.user_id or "unknown",
                status="initiated",
                type="outbound",
                to_phone=request.phone_no,
                agent_id=request.agent_id if request.agent_id else None,
                call_metadata=None,  # Will be set after metadata creation
                created_at=datetime.utcnow()
            )
            
            # Create comprehensive metadata
            metadata = {
                "name": "live agent",
                "phone_number": request.phone_no,
                "call_id": str(call_detail.id),
            }
            
            # Add optional fields only if provided
            if request.user_id:
                metadata["user_id"] = request.user_id
            if request.agent_id:
                metadata["agent_id"] = request.agent_id
            if request.systemprompt:
                metadata["system_prompt"] = request.systemprompt
            if request.transfer_no:
                metadata["transfer_no"] = request.transfer_no
            if request.context:
                metadata["context"] = request.context
            
            # Update call_detail with metadata
            # call_detail.call_metadata = json.dumps(metadata)
            db.add(call_detail)

            # Create room using phone number as room name
            try:
                await self.livekit_api.room.create_room(
                    api.CreateRoomRequest(
                        name=request.phone_no,
                        metadata=json.dumps(metadata)
                    )
                )
            except Exception as e:
                # If room already exists, update metadata
                if 'room already exists' in str(e).lower():
                    await self.livekit_api.room.update_room_metadata(
                        api.UpdateRoomMetadataRequest(
                            room=request.phone_no,
                            metadata=json.dumps(metadata)
                        )
                    )
                else:
                    raise

            trunk_id = await self.get_sip_trunk()
            print("trunk_id",trunk_id)

            # Create SIP participant
            sipRequest = CreateSIPParticipantRequest(
                sip_trunk_id=trunk_id,
                sip_call_to=request.phone_no,
                room_name=request.phone_no,
                participant_identity=f"{request.phone_no}-{call_detail.id}",
                participant_name=request.phone_no,
                krisp_enabled=True,
            )

            await self.livekit_api.sip.create_sip_participant(sipRequest)

            db.commit()

            return call_pb2.NodeOutboundCallResponse(
                success=True,
                message="Node outbound call initiated successfully",
                call_id=str(call_detail.id)
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to initiate node outbound call: {str(e)}")
            return call_pb2.NodeOutboundCallResponse(
                success=False,
                message=f"Failed to initiate node outbound call: {str(e)}"
            )
