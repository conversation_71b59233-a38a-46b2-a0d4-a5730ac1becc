import uuid
from datetime import datetime

import grpc
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.grpc import call_pb2
from app.models import Voice
from app.services.base_service import BaseService


class VoiceService(BaseService):
    def createVoice(
        self, request: call_pb2.CreateVoiceRequest, context: grpc.ServicerContext
    ) -> call_pb2.CreateVoiceResponse:
        """Create a new voice in the system."""
        db = self.get_db()
        try:
            # Check if voice already exists with same voice_id
            existing_voice = db.query(Voice).filter(Voice.voice_id == request.voice_id).first()
            if existing_voice:
                context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                context.set_details("Voice with this voice_id already exists")
                return call_pb2.CreateVoiceResponse(
                    success=False, message="Voice with this voice_id already exists"
                )

            # Create new voice instance
            new_voice = Voice(
                voice_id=request.voice_id,
                name=request.name,
                category=request.category,
                stability=request.stability,
                similarity_boost=request.similarity_boost,
                style=request.style if request.HasField("style") else None,
                speed=request.speed if request.HasField("speed") else None,
                use_speaker_boost=(
                    request.use_speaker_boost if request.HasField("use_speaker_boost") else None
                ),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow(),
            )

            # Add to database
            db.add(new_voice)
            db.commit()
            db.refresh(new_voice)

            # Convert to protobuf response
            voice_proto = call_pb2.Voice(
                id=str(new_voice.id),
                voice_id=new_voice.voice_id,
                name=new_voice.name,
                category=new_voice.category,
                stability=new_voice.stability,
                similarity_boost=new_voice.similarity_boost,
                created_at=new_voice.created_at.isoformat(),
                updated_at=new_voice.updated_at.isoformat(),
            )

            # Add optional fields if they exist
            if new_voice.style is not None:
                voice_proto.style = new_voice.style
            if new_voice.speed is not None:
                voice_proto.speed = new_voice.speed
            if new_voice.use_speaker_boost is not None:
                voice_proto.use_speaker_boost = new_voice.use_speaker_boost

            return call_pb2.CreateVoiceResponse(
                success=True, message="Voice created successfully", voice=voice_proto
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.CreateVoiceResponse(
                success=False, message=f"Failed to create voice: {str(e)}"
            )
        finally:
            db.close()

    def getVoiceById(
        self, request: call_pb2.GetVoiceByIdRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetVoiceByIdResponse:
        """Get a voice by its internal ID."""
        db = self.get_db()
        try:
            voice = db.query(Voice).filter(Voice.id == uuid.UUID(request.id)).first()
            if not voice:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Voice not found")
                return call_pb2.GetVoiceByIdResponse(success=False, message="Voice not found")

            # Convert to protobuf response
            voice_proto = call_pb2.Voice(
                id=str(voice.id),
                voice_id=voice.voice_id,
                name=voice.name,
                category=voice.category,
                stability=voice.stability,
                similarity_boost=voice.similarity_boost,
                created_at=voice.created_at.isoformat(),
                updated_at=voice.updated_at.isoformat(),
            )

            # Add optional fields if they exist
            if voice.style is not None:
                voice_proto.style = voice.style
            if voice.speed is not None:
                voice_proto.speed = voice.speed
            if voice.use_speaker_boost is not None:
                voice_proto.use_speaker_boost = voice.use_speaker_boost

            return call_pb2.GetVoiceByIdResponse(
                success=True, message="Voice retrieved successfully", voice=voice_proto
            )

        except ValueError:
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details("Invalid voice ID format")
            return call_pb2.GetVoiceByIdResponse(success=False, message="Invalid voice ID format")
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.GetVoiceByIdResponse(
                success=False, message=f"Error retrieving voice: {str(e)}"
            )
        finally:
            db.close()

    def getVoiceByVoiceId(
        self, request: call_pb2.GetVoiceByVoiceIdRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetVoiceByVoiceIdResponse:
        """Get a voice by its 11labs voice ID."""
        db = self.get_db()
        try:
            voice = db.query(Voice).filter(Voice.voice_id == request.voice_id).first()
            if not voice:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Voice not found")
                return call_pb2.GetVoiceByVoiceIdResponse(success=False, message="Voice not found")

            # Convert to protobuf response
            voice_proto = call_pb2.Voice(
                id=str(voice.id),
                voice_id=voice.voice_id,
                name=voice.name,
                category=voice.category,
                stability=voice.stability,
                similarity_boost=voice.similarity_boost,
                created_at=voice.created_at.isoformat(),
                updated_at=voice.updated_at.isoformat(),
            )

            # Add optional fields if they exist
            if voice.style is not None:
                voice_proto.style = voice.style
            if voice.speed is not None:
                voice_proto.speed = voice.speed
            if voice.use_speaker_boost is not None:
                voice_proto.use_speaker_boost = voice.use_speaker_boost

            return call_pb2.GetVoiceByVoiceIdResponse(
                success=True, message="Voice retrieved successfully", voice=voice_proto
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.GetVoiceByVoiceIdResponse(
                success=False, message=f"Error retrieving voice: {str(e)}"
            )
        finally:
            db.close()

    def listVoices(
        self, request: call_pb2.ListVoicesRequest, context: grpc.ServicerContext
    ) -> call_pb2.ListVoicesResponse:
        """List all voices with pagination and optional category filtering."""
        db = self.get_db()
        try:
            # Set pagination parameters
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            # Base query
            query = db.query(Voice)

            # Apply category filter if provided
            if request.HasField("category") and request.category:
                query = query.filter(Voice.category == request.category)

            # Get total count
            total_voices = query.count()

            # Apply pagination
            voices = query.offset(offset).limit(page_size).all()

            # Convert to protobuf response
            voice_list = []
            for voice in voices:
                voice_proto = call_pb2.Voice(
                    id=str(voice.id),
                    voice_id=voice.voice_id,
                    name=voice.name,
                    category=voice.category,
                    stability=voice.stability,
                    similarity_boost=voice.similarity_boost,
                    created_at=voice.created_at.isoformat(),
                    updated_at=voice.updated_at.isoformat(),
                )

                # Add optional fields if they exist
                if voice.style is not None:
                    voice_proto.style = voice.style
                if voice.speed is not None:
                    voice_proto.speed = voice.speed
                if voice.use_speaker_boost is not None:
                    voice_proto.use_speaker_boost = voice.use_speaker_boost

                voice_list.append(voice_proto)

            # Calculate pagination metadata
            total_pages = (total_voices + page_size - 1) // page_size
            has_next_page = page < total_pages
            has_previous_page = page > 1

            return call_pb2.ListVoicesResponse(
                data=voice_list,
                metadata=call_pb2.PaginationMetadata(
                    total=total_voices,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.ListVoicesResponse()
        finally:
            db.close()

    def updateVoice(
        self, request: call_pb2.UpdateVoiceRequest, context: grpc.ServicerContext
    ) -> call_pb2.UpdateVoiceResponse:
        """Update voice properties."""
        db = self.get_db()
        try:
            # Find the voice to update
            voice = db.query(Voice).filter(Voice.id == uuid.UUID(request.id)).first()
            if not voice:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Voice not found")
                return call_pb2.UpdateVoiceResponse(success=False, message="Voice not found")

            # Check if voice_id is being updated and if it already exists
            if request.HasField("voice_id") and request.voice_id != voice.voice_id:
                existing_voice = db.query(Voice).filter(Voice.voice_id == request.voice_id).first()
                if existing_voice:
                    context.set_code(grpc.StatusCode.ALREADY_EXISTS)
                    context.set_details("Another voice with this voice_id already exists")
                    return call_pb2.UpdateVoiceResponse(
                        success=False, message="Another voice with this voice_id already exists"
                    )
                voice.voice_id = request.voice_id

            # Update other fields if provided
            if request.HasField("name"):
                voice.name = request.name
            if request.HasField("category"):
                voice.category = request.category
            if request.HasField("stability"):
                voice.stability = request.stability
            if request.HasField("similarity_boost"):
                voice.similarity_boost = request.similarity_boost
            if request.HasField("style"):
                voice.style = request.style
            if request.HasField("speed"):
                voice.speed = request.speed
            if request.HasField("use_speaker_boost"):
                voice.use_speaker_boost = request.use_speaker_boost

            # Update timestamp
            voice.updated_at = datetime.utcnow()

            # Commit changes
            db.commit()
            db.refresh(voice)

            # Convert to protobuf response
            voice_proto = call_pb2.Voice(
                id=str(voice.id),
                voice_id=voice.voice_id,
                name=voice.name,
                category=voice.category,
                stability=voice.stability,
                similarity_boost=voice.similarity_boost,
                created_at=voice.created_at.isoformat(),
                updated_at=voice.updated_at.isoformat(),
            )

            # Add optional fields if they exist
            if voice.style is not None:
                voice_proto.style = voice.style
            if voice.speed is not None:
                voice_proto.speed = voice.speed
            if voice.use_speaker_boost is not None:
                voice_proto.use_speaker_boost = voice.use_speaker_boost

            return call_pb2.UpdateVoiceResponse(
                success=True, message="Voice updated successfully", voice=voice_proto
            )

        except ValueError:
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details("Invalid voice ID format")
            return call_pb2.UpdateVoiceResponse(success=False, message="Invalid voice ID format")
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.UpdateVoiceResponse(
                success=False, message=f"Error updating voice: {str(e)}"
            )
        finally:
            db.close()

    def deleteVoice(
        self, request: call_pb2.DeleteVoiceRequest, context: grpc.ServicerContext
    ) -> call_pb2.DeleteVoiceResponse:
        """Delete a voice from the system."""
        db = self.get_db()
        try:
            # Find the voice to delete
            voice = db.query(Voice).filter(Voice.id == uuid.UUID(request.id)).first()
            if not voice:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Voice not found")
                return call_pb2.DeleteVoiceResponse(success=False, message="Voice not found")

            # Delete the voice
            db.delete(voice)
            db.commit()

            return call_pb2.DeleteVoiceResponse(success=True, message="Voice deleted successfully")

        except ValueError:
            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
            context.set_details("Invalid voice ID format")
            return call_pb2.DeleteVoiceResponse(success=False, message="Invalid voice ID format")
        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.DeleteVoiceResponse(
                success=False, message=f"Error deleting voice: {str(e)}"
            )
        finally:
            db.close()
