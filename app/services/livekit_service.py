from typing import Dict, List, Optional, Any
from livekit import api
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class LiveKitService:
    """
    Service class for managing LiveKit SIP inbound trunks.
    """
    
    def __init__(self):
        self.livekit_api = None
    
    async def initialize(self):
        """
        Async initialization method for LiveKit API
        """
        self.livekit_api = api.LiveKitAPI(
            settings.LIVEKIT_URL, 
            settings.LIVEKIT_API_KEY, 
            settings.LIVEKIT_API_SECRET
        )
    
    def _log_error(self, message: str):
        """Log error message."""
        print(f"ERROR: {message}")


    def _log_info(self, message: str):
        """Log info message."""
        print(f"INFO: {message}")
    
    async def create_livekit_trunk(self, phone_number, trunk_name: str, request) -> str:
        """Create LiveKit trunk for the phone number."""
        
        livekit_trunk_id = None
        
        try:
            # Initialize LiveKit service if needed
            if self.livekit_api is None:
                await self.initialize()
            print("allowed:", list(request.allowed_incoming_numbers))
            trunk = api.SIPInboundTrunkInfo(
                name = trunk_name,
                numbers = [phone_number.phone_number],
                krisp_enabled = getattr(request, 'krisp_enabled', False),
                allowed_numbers=list(request.allowed_incoming_numbers)
            )
            print("trunk", trunk)
            request = api.CreateSIPInboundTrunkRequest(
                trunk = trunk
            )

            livekit_trunk = await self.livekit_api.sip.create_sip_inbound_trunk(request)
            print("livekit_trunk", livekit_trunk)
            await self.livekit_api.aclose()
            
            # livekit_trunk_id = livekit_trunk.get('sip_trunk_id')
            livekit_trunk_id = livekit_trunk.sip_trunk_id
            self._log_info(f"LiveKit trunk created successfully with ID: {livekit_trunk_id}")
        
        except Exception as livekit_error:
            self._log_error(f"LiveKit trunk creation failed: {str(livekit_error)}")
            # Re-raise the exception to fail the entire operation
            raise Exception(f"Failed to create LiveKit trunk: {str(livekit_error)}")
        
        return livekit_trunk_id

    async def update_livekit_trunk(
        self,
        trunk_id: str,
        trunk_name: Optional[str] = None,
        allowed_numbers: Optional[List[str]] = None,
        metadata: Optional[str] = None,
        krisp_enabled: Optional[bool] = None
    ) -> bool:
        """
        Update an existing SIP inbound trunk in LiveKit.
        
        Args:
            trunk_id: External LiveKit trunk ID
            trunk_name: Optional new trunk name
            allowed_numbers: Optional updated allowed caller numbers
            metadata: Optional trunk metadata
            krisp_enabled: Optional Krisp noise cancellation setting
        
        Returns:
            bool: True if update was successful
        
        Raises:
            Exception: If trunk update fails
        """
        try:
            # Initialize LiveKit service if needed
            if self.livekit_api is None:
                await self.initialize()
                
            self._log_info(f"Updating SIP inbound trunk1 {trunk_id}")
            print("inside",allowed_numbers)
            
            # Create update request with only provided fields
            trunk_update = api.SIPInboundTrunkInfo(
                sip_trunk_id=trunk_id,
                allowed_numbers=list(allowed_numbers),
                krisp_enabled=krisp_enabled,
                name=trunk_name
            )
            
            # if trunk_name is not None:
            #     trunk_update.name = trunk_name
            # if metadata is not None:
            #     trunk_update.metadata = metadata
            # if krisp_enabled is not None:
            #     trunk_update.krisp_enabled = krisp_enabled

            print("trunk_update", trunk_update)

            
            update_request = api.UpdateSIPInboundTrunkRequest(
                sip_trunk_id=trunk_id,
                replace=trunk_update
            )
            print("update_request", update_request)
            
            # Update the trunk using LiveKit API
            response = await self.livekit_api.sip.update_sip_inbound_trunk(update_request)
            print("updated SIP inbound", response)
            await self.livekit_api.aclose()
            
            self._log_info(f"Successfully updated SIP inbound trunk: {trunk_id}")
            return True
            
        except Exception as e:
            self._log_error(f"Failed to update SIP inbound trunk {trunk_id}: {str(e)}")
            raise Exception(f"LiveKit trunk update failed: {str(e)}")
    
    async def delete_livekit_trunk(self, trunk_id: str) -> bool:
        """
        Delete a SIP inbound trunk from LiveKit.
        
        Args:
            trunk_id: External LiveKit trunk ID to delete
        
        Returns:
            bool: True if deletion was successful
        
        Raises:
            Exception: If trunk deletion fails
        """
        try:
            # Initialize LiveKit service if needed
            if self.livekit_api is None:
                await self.initialize()
                
            self._log_info(f"Deleting SIP inbound trunk: {trunk_id}")
            
            # Create delete request
            delete_request = api.DeleteSIPTrunkRequest(
                sip_trunk_id=trunk_id
            )
            
            # Delete the trunk using LiveKit API
            await self.livekit_api.sip.delete_sip_trunk(delete_request)
            await self.livekit_api.aclose()
            
            self._log_info(f"Successfully deleted SIP inbound trunk: {trunk_id}")
            return True
            
        except Exception as e:
            self._log_error(f"Failed to delete SIP inbound trunk {trunk_id}: {str(e)}")
            raise Exception(f"LiveKit trunk deletion failed: {str(e)}")
    
    def get_sip_inbound_trunk(self, trunk_id: str) -> Dict[str, Any]:
        """
        Get information about a SIP inbound trunk from LiveKit.
        
        Args:
            trunk_id: External LiveKit trunk ID
        
        Returns:
            Dict containing trunk information
        
        Raises:
            Exception: If trunk retrieval fails
        """
        try:
            logger.info(f"Getting SIP inbound trunk info: {trunk_id}")
            
            # Get trunk info using LiveKit API
            # Note: This is a placeholder - you'll need to use the actual LiveKit API method
            response = self.livekit_api.sip.get_sip_inbound_trunk(trunk_id=trunk_id)
            
            logger.info(f"Successfully retrieved SIP inbound trunk: {trunk_id}")
            
            return {
                "id": response.sip_trunk_id,
                "name": response.name,
                "numbers": response.numbers,
                "allowed_numbers": response.allowed_numbers,
                # Add other fields as needed based on actual response
            }
            
        except Exception as e:
            logger.error(f"Failed to get SIP inbound trunk {trunk_id}: {str(e)}")
            raise Exception(f"LiveKit trunk retrieval failed: {str(e)}")
    
    def list_sip_inbound_trunks(self) -> List[Dict[str, Any]]:
        """
        List all SIP inbound trunks from LiveKit.
        
        Returns:
            List of Dict containing trunk information
        
        Raises:
            Exception: If trunk listing fails
        """
        try:
            logger.info("Listing all SIP inbound trunks")
            
            # List trunks using LiveKit API
            # Note: This is a placeholder - you'll need to use the actual LiveKit API method
            response = self.livekit_api.sip.list_sip_inbound_trunks()
            
            trunks = []
            for trunk in response.trunks:
                trunks.append({
                    "id": trunk.sip_trunk_id,
                    "name": trunk.name,
                    "numbers": trunk.numbers,
                    "allowed_numbers": trunk.allowed_numbers,
                    # Add other fields as needed
                })
            
            logger.info(f"Successfully listed {len(trunks)} SIP inbound trunks")
            return trunks
            
        except Exception as e:
            logger.error(f"Failed to list SIP inbound trunks: {str(e)}")
            raise Exception(f"LiveKit trunk listing failed: {str(e)}")