import uuid
import csv
import io
import re
import validators
import json
from datetime import datetime
from google.cloud import storage
from typing import Dict, List, Any

import grpc

from app.grpc import call_pb2
from app.models import Contact, Agent, ContactList
from app.services.base_service import BaseService
from app.core.config import settings


class ContactService(BaseService):
    def uploadContacts(
        self, request: call_pb2.UploadContactsRequest, context: grpc.ServicerContext
    ) -> call_pb2.UploadContactsResponse:
        """
        Uploads and processes a CSV file containing contact information.

        Args:
            request (UploadContactsRequest): Contains:
                - title (str): Title for the contact list
                - user_id (str): ID of the user uploading contacts
                - csv_url (str): GCS URL of the CSV file
                - description (str): Optional description
                - agent_id (str, optional): UUID of associated agent
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            UploadContactsResponse: Contains:
                - success (bool): True if upload successful, False otherwise
                - message (str): Description of the operation result

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If agent_id format is invalid or agent metadata not found
                - INTERNAL: For CSV processing errors or unexpected server errors
        """
        db = self.get_db()
        try:
            # Convert agent_id string to UUID if provided
            agent_id = None
            if request.agent_id:
                try:
                    agent_id = uuid.UUID(request.agent_id)
                except ValueError:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid agent_id format")
                    return call_pb2.UploadContactsResponse(
                        success=False, message="Invalid agent_id format"
                    )

            # Create new contact
            new_contact = Contact(
                id=str(uuid.uuid4()),
                title=request.title,
                user_id=request.user_id,
                csv_url=request.csv_url,
                description=request.description,
                uploaded_at=datetime.utcnow(),
                agent_id=agent_id,
            )
            db.add(new_contact)
            db.commit()
            db.refresh(new_contact)

            # Get agent's CSV metadata for validation
            agent = None
            if agent_id:
                agent = db.query(Agent).filter(Agent.id == agent_id).first()
                if not agent or not agent.csv_metadata:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Agent CSV metadata not found")
                    return call_pb2.UploadContactsResponse(
                        success=False, message="Agent CSV metadata not found"
                    )

            # Download and process CSV
            try:
                csv_content = self._download_gcs_file(f"{settings.GCS_BASE_URL}{request.csv_url}")
                csv_reader = csv.DictReader(io.StringIO(csv_content))
                
                # Process each row
                for row in csv_reader:
                    if 'phoneNumber' not in row:
                        # Store all fields as metadata when phoneNumber is missing
                        contact_list = ContactList(
                            contact_id=new_contact.id,
                            phone_number="",  # Empty phone number
                            is_valid=False,   # Mark as invalid
                            csv_metadata=row, # Store all fields as metadata
                            created_at=datetime.utcnow(),
                            updated_at=datetime.utcnow()
                        )
                        db.add(contact_list)
                        continue

                    is_valid = True
                    if agent and agent.csv_metadata:
                        is_valid = self._validate_row(row, agent.csv_metadata)

                    # Store all fields except phoneNumber in metadata
                    metadata = {k: v for k, v in row.items() if k != 'phoneNumber'}
                    
                    contact_list = ContactList(
                        contact_id=new_contact.id,
                        phone_number=row['phoneNumber'],
                        is_valid=is_valid,
                        csv_metadata=metadata,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    db.add(contact_list)

                db.commit()
                return call_pb2.UploadContactsResponse(
                    success=True,
                    message="Contact list uploaded and processed successfully",
                )

            except Exception as e:
                db.rollback()
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Failed to process CSV: {str(e)}")
                return call_pb2.UploadContactsResponse(
                    success=False,
                    message=f"Failed to process CSV: {str(e)}"
                )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.UploadContactsResponse()
        finally:
            db.close()

    def getContacts(
        self, request: call_pb2.GetContactsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetContactsResponse:
        """
        Retrieves a paginated list of contacts for a specific user.

        Args:
            request (GetContactsRequest): Contains:
                - user_id (str): ID of the user to get contacts for
                - page (int): Page number for pagination (default: 1)
                - pageSize (int): Number of items per page (default: 10)
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetContactsResponse: Contains:
                - data (List[Contact]): List of contacts with details:
                    - contact_id (str): UUID of the contact
                    - title (str): Contact list title
                    - csv_url (str): GCS URL of the CSV file
                    - description (str): Contact list description
                    - uploaded_at (str): ISO formatted upload timestamp
                    - agent_id (str): Associated agent's UUID if any
                - metadata (PaginationMetadata): Pagination information:
                    - total (int): Total number of contacts
                    - totalPages (int): Total number of pages
                    - currentPage (int): Current page number
                    - pageSize (int): Items per page
                    - hasNextPage (bool): Whether there's a next page
                    - hasPreviousPage (bool): Whether there's a previous page

        Raises:
            gRPC Status Codes:
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            page = request.page if request.page > 0 else 1
            page_size = request.pageSize if request.pageSize > 0 else 10

            # Filter by user_id
            user_id = request.user_id
            user_contacts_query = db.query(Contact).filter(Contact.user_id == user_id)

            # Get total count after filtering
            total = user_contacts_query.count()

            # Apply pagination
            contacts_query = (
                user_contacts_query.offset((page - 1) * page_size).limit(page_size).all()
            )
            total_pages = (total + page_size - 1) // page_size

            # Calculate hasNextPage and hasPreviousPage
            has_next_page = page < total_pages
            has_previous_page = page > 1

            contacts = [
                call_pb2.Contact(
                    contact_id=str(contact.id),
                    title=contact.title,
                    csv_url=contact.csv_url,
                    description=contact.description if contact.description else "",
                    uploaded_at=contact.uploaded_at.isoformat(),
                    agent_id=str(contact.agent_id) if contact.agent_id else "",
                )
                for contact in contacts_query
            ]

            # Return response with updated structure
            return call_pb2.GetContactsResponse(
                data=contacts,
                metadata=call_pb2.PaginationMetadata(
                    total=total,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.GetContactsResponse()
        finally:
            db.close()

    def getContactById(
        self, request: call_pb2.GetContactByIdRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetContactByIdResponse:
        """
        Retrieves detailed information about a specific contact.

        Args:
            request (GetContactByIdRequest): Contains:
                - contact_id (str): UUID of the contact to retrieve
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetContactByIdResponse: Contains:
                - contact_id (str): UUID of the contact
                - user_id (str): ID of the user who owns the contact
                - title (str): Contact list title
                - csv_url (str): GCS URL of the CSV file
                - description (str): Contact list description
                - uploaded_at (str): ISO formatted upload timestamp
                - agent_id (str): Associated agent's UUID if any

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If contact_id format is invalid
                - NOT_FOUND: If contact doesn't exist
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Convert contact_id string to UUID
            try:
                contact_id = uuid.UUID(request.contact_id)
            except ValueError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid contact_id format")
                return call_pb2.GetContactByIdResponse()

            # Query the contact by ID
            contact = db.query(Contact).filter(Contact.id == contact_id).first()
            if not contact:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Contact not found")
                return call_pb2.GetContactByIdResponse()

            # Return the contact details
            return call_pb2.GetContactByIdResponse(
                contact_id=str(contact.id),
                user_id=contact.user_id,
                title=contact.title,
                csv_url=contact.csv_url,
                description=contact.description if contact.description else "",
                uploaded_at=contact.uploaded_at.isoformat(),
                agent_id=str(contact.agent_id) if contact.agent_id else "",
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.GetContactByIdResponse()
        finally:
            db.close()

    def getContactLists(
        self, request: call_pb2.GetContactListsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetContactListsResponse:
        """
        Retrieves a paginated list of contact entries for a specific contact list.

        Args:
            request (GetContactListsRequest): Contains:
                - contact_id (str): UUID of the contact list
                - page (int): Page number for pagination (default: 1)
                - pageSize (int): Number of items per page (default: 10)
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetContactListsResponse: Contains:
                - items (List[ContactListItem]): List of contact entries with details:
                    - phone_number (str): Contact's phone number
                    - is_valid (bool): Validation status of the contact
                    - csv_metadata (str): JSON string of additional CSV fields
                    - created_at (str): ISO formatted creation timestamp
                    - updated_at (str): ISO formatted last update timestamp
                - metadata (PaginationMetadata): Pagination information:
                    - total (int): Total number of contact entries
                    - totalPages (int): Total number of pages
                    - currentPage (int): Current page number
                    - pageSize (int): Items per page
                    - hasNextPage (bool): Whether there's a next page
                    - hasPreviousPage (bool): Whether there's a previous page

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If contact_id format is invalid
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Convert contact_id string to UUID and set pagination parameters
            try:
                contact_id = uuid.UUID(request.contact_id)
            except ValueError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid contact_id format")
                return call_pb2.GetContactListsResponse()

            page = request.page if request.page > 0 else 1
            page_size = request.pageSize if request.pageSize > 0 else 10
            offset = (page - 1) * page_size

            # Base query
            base_query = db.query(ContactList).filter(ContactList.contact_id == contact_id)

            # Get total count
            total_count = base_query.count()

            # Apply pagination
            contact_lists = (
                base_query
                .order_by(ContactList.created_at.desc())
                .offset(offset)
                .limit(page_size)
                .all()
            )

            # Convert to proto message
            contact_list_items = []
            for cl in contact_lists:
                metadata_str = json.dumps(cl.csv_metadata) if cl.csv_metadata else ""
                
                contact_list_items.append(
                    call_pb2.ContactListItem(
                        phone_number=cl.phone_number,
                        is_valid=bool(cl.is_valid),
                        csv_metadata=metadata_str,
                        created_at=cl.created_at.isoformat(),
                        updated_at=cl.updated_at.isoformat()
                    )
                )

            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0
            has_next_page = page < total_pages
            has_previous_page = page > 1

            return call_pb2.GetContactListsResponse(
                data=contact_list_items,
                metadata=call_pb2.PaginationMetadata(
                    total=total_count,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                )
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.GetContactListsResponse()
        finally:
            db.close()

    def getAllContacts(
        self, request: call_pb2.GetAllContactsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetAllContactsResponse:
        db = self.get_db()
        try:
            page = request.page if request.page > 0 else 1
            page_size = request.pageSize if request.pageSize > 0 else 10

            # Base query for all contacts
            contacts_query = db.query(Contact)

            # Get total count
            total = contacts_query.count()

            # Apply pagination
            contacts = (
                contacts_query
                .order_by(Contact.uploaded_at.desc())
                .offset((page - 1) * page_size)
                .limit(page_size)
                .all()
            )

            # Calculate pagination metadata
            total_pages = (total + page_size - 1) // page_size
            has_next_page = page < total_pages
            has_previous_page = page > 1

            # Convert contacts to response format
            contact_list = [
                call_pb2.Contact(
                    contact_id=str(contact.id),
                    title=contact.title,
                    csv_url=contact.csv_url,
                    description=contact.description if contact.description else "",
                    uploaded_at=contact.uploaded_at.isoformat(),
                    agent_id=str(contact.agent_id) if contact.agent_id else "",
                )
                for contact in contacts
            ]

            # Return response with data and metadata
            return call_pb2.GetAllContactsResponse(
                data=contact_list,
                metadata=call_pb2.PaginationMetadata(
                    total=total,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error fetching contacts: {str(e)}")
            return call_pb2.GetAllContactsResponse()

        finally:
            db.close()

    def _download_gcs_file(self, gcs_url: str) -> str:
        """Download file from Google Cloud Storage using public HTTP URL."""
        import requests
        
        try:
            response = requests.get(gcs_url)
            response.raise_for_status()  # Raise an exception for bad status codes
            return response.text
            
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to download CSV file: {str(e)}")

    def _validate_row(self, row: Dict[str, str], csv_metadata: List[Dict[str, str]]) -> bool:
        """Validate row against CSV metadata."""
        for field_meta in csv_metadata:
            field = field_meta.get('field')
            verification = field_meta.get('verification')
            
            if not field or not verification or field not in row:
                continue

            value = row[field]
            if not self._validate_field(value, verification):
                return False
        
        return True

    def _validate_field(self, value: str, verification_type: str) -> bool:
        """Validate a single field based on verification type."""
        if not value:
            return False

        verification_type = verification_type.lower()
        
        if verification_type == 'phone number':
            # Basic phone number validation (can be enhanced based on requirements)
            phone_pattern = re.compile(r'^\+?1?\d{9,15}$')
            return bool(phone_pattern.match(value))
        
        elif verification_type == 'email':
            return bool(validators.email(value))
        
        elif verification_type == 'alphanumeric':
            return value.isalnum()
        
        elif verification_type == 'number':
            return value.replace('.', '').isdigit()
        
        elif verification_type == 'date':
            try:
                datetime.strptime(value, '%Y-%m-%d')
                return True
            except ValueError:
                return False
        
        elif verification_type == 'url':
            return bool(validators.url(value))
        
        elif verification_type == 'zip code':
            # Basic ZIP code validation (can be enhanced based on requirements)
            return len(value) >= 5 and value.isalnum()
        
        return False
