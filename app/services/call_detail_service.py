from datetime import datetime
from uuid import UUID
import json

import grpc
from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session

from app.grpc import call_pb2
from app.models import CallDetail
from app.services.base_service import BaseService


class CallDetailService(BaseService):
    def getAllCalls(
        self, request: call_pb2.GetAllCallsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetAllCallsResponse:
        """
        Retrieves all call details with pagination.
        """
        db: Session = self.get_db()
        try:
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            total_calls = (
                db.query(func.count(CallDetail.id))
                .scalar()
            )

            call_details = (
                db.query(CallDetail)
                .order_by(CallDetail.created_at.desc())
                .limit(page_size)
                .offset(offset)
                .all()
            )

            call_list = [
                call_pb2.CallDetail(
                    id=str(detail.id),
                    user_id=detail.user_id,
                    batch_call_id=str(detail.batch_call_id) if detail.batch_call_id else "",
                    call_duration=detail.call_duration or 0,
                    recording_url=str(detail.recording_url) or "",
                    transcription_url=str(detail.transcription_url) or "",
                    status=detail.status,
                    call_failing_reason=detail.call_failing_reason or "",
                    created_at=detail.created_at.isoformat(),
                    # Add new fields
                    type=detail.type or "",
                    from_phone=detail.from_phone or "",
                    to_phone=detail.to_phone or "",
                    transfer_status=detail.transfer_status or "",
                    additional_guidelines=detail.additional_guidelines or "",
                    call_metadata=detail.call_metadata or "",
                    month=detail.month or 0,
                    year=detail.year or 0,
                    billed=detail.billed,
                    invoice_id=detail.invoice_id or "",
                    agent_id=detail.agent_id or "",
                    avg_latency=detail.avg_latency or 0.0,
                    cost=detail.cost or 0.0,
                    cost_breakdown=json.dumps(detail.cost_breakdown) if detail.cost_breakdown else "",
                    latency_breakdown=json.dumps(detail.latency_breakdown) if detail.latency_breakdown else "",
                )
                for detail in call_details
            ]

            total_pages = (total_calls + page_size - 1) // page_size if total_calls > 0 else 0
            has_next_page = page < total_pages
            has_previous_page = page > 1

            return call_pb2.GetAllCallsResponse(
                data=call_list,
                metadata=call_pb2.PaginationMetadata(
                    total=total_calls,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error fetching calls: {str(e)}")
            return call_pb2.GetAllCallsResponse()

        finally:
            db.close()

    def storeCallDetails(
        self, request: call_pb2.StoreCallDetailsRequest, context: grpc.ServicerContext
    ) -> call_pb2.StoreCallDetailsResponse:
        """
        Store call details with optional batch_call_id.

        Args:
            request (StoreCallDetailsRequest): Request containing call details
            context (grpc.ServicerContext): gRPC service context

        Returns:
            StoreCallDetailsResponse: Response indicating success or failure
        """
        db = self.get_db()
        try:
            call_detail_args = {
                "user_id": request.user_id,
                "call_duration": request.call_duration,
                "recording_url": request.recording_url or None,
                "transcription_url": request.transcription_url or None,
                "status": request.status,
                "call_failing_reason": request.call_failing_reason or None,
                "created_at": datetime.utcnow(),
                "type": request.type or None,
                "from_phone": request.from_phone or None,
                "to_phone": request.to_phone or None,
                "transfer_status": request.transfer_status or None,
                "additional_guidelines": request.additional_guidelines or None,
                "call_metadata": request.call_metadata or None,
                "month": request.month or None,
                "year": request.year or None,
                "billed": request.billed,
                "invoice_id": request.invoice_id or None,
                "avg_latency": request.avg_latency or None,
                "cost": request.cost or None,
            }

            # Handle cost_breakdown if provided
            if hasattr(request, 'cost_breakdown') and request.cost_breakdown:
                try:
                    # Parse JSON string to validate structure
                    cost_breakdown_data = json.loads(request.cost_breakdown)
                    call_detail_args["cost_breakdown"] = cost_breakdown_data
                except json.JSONDecodeError as e:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid cost_breakdown JSON format: {str(e)}")
                    return call_pb2.StoreCallDetailsResponse(
                        success=False, message="Invalid cost_breakdown JSON format"
                    )

            # Handle latency_breakdown if provided
            if hasattr(request, 'latency_breakdown') and request.latency_breakdown:
                try:
                    # Parse JSON string to validate structure
                    latency_breakdown_data = json.loads(request.latency_breakdown)
                    call_detail_args["latency_breakdown"] = latency_breakdown_data
                except json.JSONDecodeError as e:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid latency_breakdown JSON format: {str(e)}")
                    return call_pb2.StoreCallDetailsResponse(
                        success=False, message="Invalid latency_breakdown JSON format"
                    )

            # Conditionally add batch_call_id if provided
            if request.batch_call_id:
                try:
                    call_detail_args["batch_call_id"] = UUID(request.batch_call_id)
                except ValueError:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid batch_call_id format")
                    return call_pb2.StoreCallDetailsResponse(
                        success=False, message="Invalid batch_call_id"
                    )

            # Conditionally add agent_id if provided (now accepts any string format)
            if request.agent_id:
                call_detail_args["agent_id"] = request.agent_id

            # Create new call detail instance
            new_call_detail = CallDetail(**call_detail_args)

            # Database operations
            db.add(new_call_detail)
            db.commit()
            db.refresh(new_call_detail)

            return call_pb2.StoreCallDetailsResponse(
                call_id=str(new_call_detail.id),
                success=True,
                message="Call details stored successfully",
            )

        except SQLAlchemyError as db_error:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Database error: {str(db_error)}")
            return call_pb2.StoreCallDetailsResponse(
                success=False, message="Database error occurred"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return call_pb2.StoreCallDetailsResponse(success=False, message="Internal server error")

        finally:
            db.close()

    def getCallDetails(
        self, request: call_pb2.GetCallDetailsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetCallDetailsResponse:
        """
        Retrieves detailed information about a specific call.

        Args:
            request (GetCallDetailsRequest): Contains:
                - call_id (str): UUID of the call to retrieve details for
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetCallDetailsResponse: Contains:
                - id (str): UUID of the call
                - user_id (str): ID of the user
                - batch_call_id (str): ID of associated batch call
                - call_duration (int): Duration in seconds
                - recording_url (str): URL to call recording
                - transcription_url (str): URL to call transcription
                - status (str): Current call status
                - call_failing_reason (str): Failure reason if any
                - created_at (str): ISO formatted creation timestamp
                - type (str): Type of call
                - from_phone (str): Source phone number
                - to_phone (str): Destination phone number
                - transfer_status (str): Transfer status
                - additional_guidelines (str): Additional guidelines
                - call_metadata (str): JSON string of call metadata
                - month (int): Call month
                - year (int): Call year
                - billed (bool): Billing status
                - invoice_id (str): Associated invoice ID
                - agent_id (str): UUID of associated agent

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If call_id format is invalid
                - NOT_FOUND: If call details don't exist
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()

        try:
            if not request.batch_call_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("User ID cannot be empty")
                return call_pb2.GetUserCallsResponse(data=[])

            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            # Create a query first instead of retrieving all records
            query = (
                db.query(CallDetail)
                .filter(CallDetail.batch_call_id == request.batch_call_id)
            )

            # Get total count for pagination
            total_count = query.count()

            # Apply pagination and ordering
            query = query.order_by(CallDetail.created_at.desc())
            call_details = query.offset(offset).limit(page_size).all()

            # Return empty response with metadata if no records found
            if not call_details:
                return call_pb2.GetCallDetailsResponse(
                    data=[],
                    metadata=call_pb2.PaginationMetadata(
                        total=0,
                        totalPages=0,
                        currentPage=page,
                        pageSize=page_size,
                        hasNextPage=False,
                        hasPreviousPage=False,
                    ),
                )

            call_details_list = [
                call_pb2.CallDetail(
                    id=str(detail.id),
                    user_id=detail.user_id,
                    batch_call_id=str(detail.batch_call_id) if detail.batch_call_id else "",
                    call_duration=detail.call_duration or 0,
                    recording_url=str(detail.recording_url) or "",
                    transcription_url=str(detail.transcription_url) or "",
                    status=detail.status,
                    call_failing_reason=detail.call_failing_reason or "",
                    created_at=detail.created_at.isoformat(),
                    type=detail.type or "",
                    from_phone=detail.from_phone or "",
                    to_phone=detail.to_phone or "",
                    transfer_status=detail.transfer_status or "",
                    additional_guidelines=detail.additional_guidelines or "",
                    call_metadata=detail.call_metadata or "",
                    month=detail.month or 0,
                    year=detail.year or 0,
                    billed=detail.billed,
                    invoice_id=detail.invoice_id or "",
                    agent_id=detail.agent_id or "",
                    avg_latency=detail.avg_latency or 0.0,
                    cost=detail.cost or 0.0,
                    cost_breakdown=json.dumps(detail.cost_breakdown) if detail.cost_breakdown else "",
                )
                for detail in call_details
            ]

            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0
            has_next_page = page < total_pages
            has_previous_page = page > 1

            return call_pb2.GetCallDetailsResponse(
                data=call_details_list,
                metadata=call_pb2.PaginationMetadata(
                    total=total_count,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return call_pb2.GetCallDetailsResponse(data=[], metadata=None)

        finally:
            db.close()

    def updateCallDetails(
        self, request: call_pb2.UpdateCallDetailsRequest, context: grpc.ServicerContext
    ) -> call_pb2.UpdateCallDetailsResponse:
        """
        Updates the details of an existing call record.

        Args:
            request (UpdateCallDetailsRequest): Contains:
                - call_id (str): UUID of the call to update
                - call_duration (int, optional): Duration of the call in seconds
                - recording_url (str, optional): URL to the call recording
                - transcription_url (str, optional): URL to the call transcription
                - status (str, optional): Current status of the call
                - call_failing_reason (str, optional): Reason for call failure if any
                - type (str, optional): Type of the call
                - from_phone (str, optional): Source phone number
                - to_phone (str, optional): Destination phone number
                - transfer_status (str, optional): Status of call transfer
                - additional_guidelines (str, optional): Additional call guidelines
                - call_metadata (str, optional): JSON string of call metadata
                - month (int, optional): Month of the call
                - year (int, optional): Year of the call
                - billed (bool, optional): Billing status of the call
                - invoice_id (str, optional): Associated invoice ID
                - agent_id (str, optional): UUID of the associated agent

        Returns:
            UpdateCallDetailsResponse: Contains:
                - success (bool): True if update successful, False otherwise
                - message (str): Description of the operation result

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If call_id or agent_id format is invalid
                - NOT_FOUND: If call detail record doesn't exist
                - INTERNAL: For database errors or unexpected server errors
        """
        db = self.get_db()
        try:
            # Convert call_id to UUID and find the call detail
            try:
                call_uuid = UUID(request.call_id)
            except ValueError:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid call_id format")
                return call_pb2.UpdateCallDetailsResponse(
                    success=False, message="Invalid call_id format"
                )

            call_detail = db.query(CallDetail).filter(CallDetail.id == call_uuid).first()
            if not call_detail:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Call detail not found")
                return call_pb2.UpdateCallDetailsResponse(
                    success=False, message="Call detail not found"
                )

            # Update fields if provided in request
            if request.call_duration:
                call_detail.call_duration = request.call_duration
            if request.recording_url:
                call_detail.recording_url = request.recording_url
            if request.transcription_url:
                call_detail.transcription_url = request.transcription_url
            if request.status:
                call_detail.status = request.status
            if request.call_failing_reason:
                call_detail.call_failing_reason = request.call_failing_reason
            if request.type:
                call_detail.type = request.type
            if request.from_phone:
                call_detail.from_phone = request.from_phone
            if request.to_phone:
                call_detail.to_phone = request.to_phone
            if request.transfer_status:
                call_detail.transfer_status = request.transfer_status
            if request.additional_guidelines:
                call_detail.additional_guidelines = request.additional_guidelines
            if request.call_metadata:
                call_detail.call_metadata = request.call_metadata
            if request.month:
                call_detail.month = request.month
            if request.year:
                call_detail.year = request.year
            if request.billed is not None:
                call_detail.billed = request.billed
            if request.invoice_id:
                call_detail.invoice_id = request.invoice_id
            if request.avg_latency:
                call_detail.avg_latency = request.avg_latency
            if request.cost:
                call_detail.cost = request.cost

            # Handle cost_breakdown update if provided
            if hasattr(request, 'cost_breakdown') and request.cost_breakdown:
                try:
                    # Parse JSON string to validate structure
                    cost_breakdown_data = json.loads(request.cost_breakdown)
                    call_detail.cost_breakdown = cost_breakdown_data
                except json.JSONDecodeError as e:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid cost_breakdown JSON format: {str(e)}")
                    return call_pb2.UpdateCallDetailsResponse(
                        success=False, message="Invalid cost_breakdown JSON format"
                    )

            # Handle latency_breakdown update if provided
            if hasattr(request, 'latency_breakdown') and request.latency_breakdown:
                try:
                    # Parse JSON string to validate structure
                    latency_breakdown_data = json.loads(request.latency_breakdown)
                    call_detail.latency_breakdown = latency_breakdown_data
                except json.JSONDecodeError as e:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid latency_breakdown JSON format: {str(e)}")
                    return call_pb2.UpdateCallDetailsResponse(
                        success=False, message="Invalid latency_breakdown JSON format"
                    )

            # Handle agent_id update if provided (now accepts any string format)
            if request.agent_id:
                call_detail.agent_id = request.agent_id

            # Commit changes
            db.commit()
            db.refresh(call_detail)

            # Print debug information after update
            print("Call detail after update:")
            print(f"ID: {call_detail.id}")
            print(f"Status: {call_detail.status}")
            print(f"Call Duration: {call_detail.call_duration}")

            return call_pb2.UpdateCallDetailsResponse(
                success=True, message="Call details updated successfully"
            )

        except SQLAlchemyError as db_error:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Database error: {str(db_error)}")
            return call_pb2.UpdateCallDetailsResponse(
                success=False, message="Database error occurred"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Unexpected error: {str(e)}")
            return call_pb2.UpdateCallDetailsResponse(
                success=False, message="Internal server error"
            )

        finally:
            db.close()

    def getUserCalls(
        self, request: call_pb2.GetUserCallsRequest, context: grpc.ServicerContext
    ) -> call_pb2.GetUserCallsResponse:
        """
        Retrieves a paginated list of call details for a specific user.

        Args:
            request (GetUserCallsRequest): Contains:
                - user_id (str): ID of the user to get calls for
                - page (int): Page number for pagination (default: 1)
                - page_size (int): Number of items per page (default: 10)
            context (grpc.ServicerContext): gRPC service context for error handling

        Returns:
            GetUserCallsResponse: Contains:
                - data (List[UserCalls]): List of call details with:
                    - id (str): UUID of the call
                    - user_id (str): ID of the user
                    - batch_call_id (str): ID of associated batch call if any
                    - contact_id (str): ID of associated contact if any
                    - call_duration (int): Duration in seconds
                    - recording_url (str): URL to call recording
                    - transcription_url (str): URL to call transcription
                    - status (str): Current call status
                    - call_failing_reason (str): Failure reason if any
                    - created_at (str): ISO formatted creation timestamp
                    - type (str): Type of call
                    - from_phone (str): Source phone number
                    - to_phone (str): Destination phone number
                    - transfer_status (str): Transfer status
                    - additional_guidelines (str): Additional guidelines
                    - call_metadata (str): JSON string of call metadata
                    - month (int): Call month
                    - year (int): Call year
                    - billed (bool): Billing status
                    - invoice_id (str): Associated invoice ID
                    - agent_id (str): UUID of associated agent
                - metadata (PaginationMetadata): Pagination information:
                    - total (int): Total number of calls
                    - totalPages (int): Total number of pages
                    - currentPage (int): Current page number
                    - pageSize (int): Items per page
                    - hasNextPage (bool): Whether there's a next page
                    - hasPreviousPage (bool): Whether there's a previous page

        Raises:
            gRPC Status Codes:
                - INVALID_ARGUMENT: If user_id is empty
                - INTERNAL: For unexpected server errors
        """
        db = self.get_db()
        try:
            # Validate user_id
            if not request.user_id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("User ID cannot be empty")
                return call_pb2.GetUserCallsResponse()

            # Set pagination parameters
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10
            offset = (page - 1) * page_size

            # Base query for user calls with scheduled status only
            query = db.query(CallDetail).filter(
                CallDetail.user_id == request.user_id,
            )

            # Count total records
            total_count = query.count()

            # Apply pagination
            query = query.order_by(CallDetail.created_at.desc())
            calls = query.offset(offset).limit(page_size).all()

            # Build response data
            user_calls = []
            for call in calls:
                user_call = call_pb2.UserCalls(
                    id=str(call.id),
                    user_id=call.user_id,
                    batch_call_id=str(call.batch_call_id) if call.batch_call_id else "",
                    contact_id=(
                        ""
                        if not hasattr(call, "batch_call") or not call.batch_call
                        else str(call.batch_call.contact_id)
                    ),
                    call_duration=call.call_duration or 0,
                    recording_url=str(call.recording_url) or "",
                    transcription_url=str(call.transcription_url) or "",
                    status=call.status,
                    call_failing_reason=call.call_failing_reason or "",
                    created_at=call.created_at.isoformat(),
                    type=call.type or "",
                    from_phone=call.from_phone or "",
                    to_phone=call.to_phone or "",
                    transfer_status=call.transfer_status or "",
                    additional_guidelines=call.additional_guidelines or "",
                    call_metadata=call.call_metadata or "",
                    month=call.month or 0,
                    year=call.year or 0,
                    billed=call.billed,
                    invoice_id=call.invoice_id or "",
                    agent_id=call.agent_id or "",
                    avg_latency=call.avg_latency or 0.0,
                    cost=call.cost or 0.0,
                    cost_breakdown=json.dumps(call.cost_breakdown) if call.cost_breakdown else "",
                    latency_breakdown=json.dumps(call.latency_breakdown) if call.latency_breakdown else "",
                )
                user_calls.append(user_call)

            # Calculate pagination metadata
            total_pages = (total_count + page_size - 1) // page_size if total_count > 0 else 0
            has_next_page = page < total_pages
            has_previous_page = page > 1

            # Return response with data and metadata
            return call_pb2.GetUserCallsResponse(
                data=user_calls,
                metadata=call_pb2.PaginationMetadata(
                    total=total_count,
                    totalPages=total_pages,
                    currentPage=page,
                    pageSize=page_size,
                    hasNextPage=has_next_page,
                    hasPreviousPage=has_previous_page,
                ),
            )

        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Error retrieving user calls: {str(e)}")
            return call_pb2.GetUserCallsResponse()

        finally:
            db.close()

