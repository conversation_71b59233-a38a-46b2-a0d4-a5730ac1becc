"""Add latency_breakdown field to call_detail

Revision ID: add_latency_breakdown_field
Revises: add_cost_breakdown_field
Create Date: 2025-01-03 15:36:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_latency_breakdown_field'
down_revision = 'add_cost_breakdown_field'
branch_labels = None
depends_on = None


def upgrade():
    # Add latency_breakdown column to call_details table
    op.add_column('call_details', sa.Column('latency_breakdown', postgresql.JSONB(astext_type=sa.Text()), nullable=True))


def downgrade():
    # Remove latency_breakdown column from call_details table
    op.drop_column('call_details', 'latency_breakdown')