"""Add cost_breakdown field to call_detail

Revision ID: add_cost_breakdown_field
Revises: remove_agent_fk_constraint
Create Date: 2025-06-23 13:58:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_cost_breakdown_field'
down_revision = 'remove_agent_fk_constraint'
branch_labels = None
depends_on = None


def upgrade():
    # Add cost_breakdown column to call_details table
    op.add_column('call_details', sa.Column('cost_breakdown', postgresql.JSONB(astext_type=sa.Text()), nullable=True))


def downgrade():
    # Remove cost_breakdown column from call_details table
    op.drop_column('call_details', 'cost_breakdown')