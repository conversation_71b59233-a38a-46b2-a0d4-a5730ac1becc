"""rename_total_turns_to_cost

Revision ID: 7a6bdde00277
Revises: 
Create Date: 2025-06-10 11:50:17.085808

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '7a6bdde00277'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Rename column total_turns to cost and change type to Float in call_details table
    op.alter_column('call_details', 'total_turns', new_column_name='cost', type_=sa.Float)

def downgrade():
    # Rename column cost back to total_turns and change type back to Integer in call_details table
    op.alter_column('call_details', 'cost', new_column_name='total_turns', type_=sa.Integer)
