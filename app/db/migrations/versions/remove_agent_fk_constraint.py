"""Remove foreign key constraint from call_details.agent_id

Revision ID: remove_agent_fk_constraint
Revises: 7a6bdde00277
Create Date: 2025-06-16 17:16:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'remove_agent_fk_constraint'
down_revision = '7a6bdde00277'
branch_labels = None
depends_on = None


def upgrade():
    # Drop the foreign key constraint
    op.drop_constraint('call_details_agent_id_fkey', 'call_details', type_='foreignkey')
    
    # Change the column type from UUID to VARCHAR(255)
    op.alter_column('call_details', 'agent_id',
                    existing_type=postgresql.UUID(),
                    type_=sa.String(255),
                    existing_nullable=True,
                    postgresql_using='agent_id::text')
    
    # Add index for performance
    op.create_index('idx_call_details_agent_id', 'call_details', ['agent_id'])


def downgrade():
    # Drop the index
    op.drop_index('idx_call_details_agent_id', table_name='call_details')
    
    # Change the column type back to UUID
    op.alter_column('call_details', 'agent_id',
                    existing_type=sa.String(255),
                    type_=postgresql.UUID(),
                    existing_nullable=True,
                    postgresql_using='agent_id::uuid')
    
    # Recreate the foreign key constraint
    op.create_foreign_key('call_details_agent_id_fkey', 'call_details', 'agents', 
                         ['agent_id'], ['id'], ondelete='SET NULL')