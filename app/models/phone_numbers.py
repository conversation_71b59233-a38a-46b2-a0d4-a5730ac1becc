import uuid
from datetime import datetime
from sqlalchemy import Column, DateTime, String, Boolean, Text, Index, ARRAY
from sqlalchemy.dialects.postgresql import UUID
from app.models.base import Base


class PhoneNumber(Base):
    """
    Stores phone numbers with associated user, agent, and SIP trunk information.
    """
    __tablename__ = "phone_numbers"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String, nullable=True)
    agent_id = Column(String(255), nullable=True)  # Changed from UUID foreign key to string
    phone_number = Column(String(20), nullable=False, unique=True)
    
    # Store allowed incoming numbers as array of strings
    allowed_incoming_numbers = Column(ARRAY(String), nullable=True, default=[])
    
    # Store the external LiveKit trunk ID directly as string for quick access
    sip_inbound_trunk_id = Column(String(255), nullable=True)
    
    # Additional useful fields
    status = Column(String(20), default='active', nullable=False)  # active, inactive, suspended
    trunk_name = Column(String(255), nullable=True)  # Store trunk name for reference
    configuration = Column(Text, nullable=True)  # JSON string for additional config
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Indexes for better query performance
    __table_args__ = (
        Index('idx_phone_numbers_agent_id', 'agent_id'),
        Index('idx_phone_numbers_user_id', 'user_id'),
        Index('idx_phone_numbers_phone_number', 'phone_number'),
        Index('idx_phone_numbers_status', 'status'),
    )
    
    def __repr__(self):
        return f"<PhoneNumber id={self.id} user_id={self.user_id} phone_number={self.phone_number} status={self.status}>"