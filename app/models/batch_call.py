import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models.base import Base


class BatchCall(Base):
    """
    Stores batch calling sessions.
    """

    __tablename__ = "batch_calls"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String, nullable=False)
    contact_id = Column(
        UUID(as_uuid=True), ForeignKey("contacts.id", ondelete="CASCADE"), nullable=False
    )
    title = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    additional_guidelines = Column(Text, nullable=True)
    transfer_to = Column(String(20), nullable=True)
    service_provider = Column(String(100), nullable=True)
    schedule_time = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    status = Column(String(50), default="pending", nullable=False)

    # Relationships
    contact = relationship("Contact")
    call_details = relationship(
        "CallDetail", back_populates="batch_call", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<BatchCall id={self.id} user_id={self.user_id}>"
