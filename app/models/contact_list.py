import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, String, Boolean, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models.base import Base


class ContactList(Base):
    """
    Stores individual contacts from uploaded CSV files with validation status.
    """
    __tablename__ = "contact_lists"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    contact_id = Column(
        UUID(as_uuid=True), 
        ForeignKey("contacts.id", ondelete="CASCADE"), 
        nullable=False
    )
    phone_number = Column(String, nullable=False)
    is_valid = Column(Boolean, default=True, nullable=False)
    csv_metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationship
    contact = relationship("Contact", back_populates="contact_lists")

    def __repr__(self):
        return f"<ContactList id={self.id} contact_id={self.contact_id} phone_number={self.phone_number}>"
