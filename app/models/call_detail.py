import uuid
from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, ForeignKey, Integer, String, Text, Float
from sqlalchemy.dialects.postgresql import JSON<PERSON>, UUID
from sqlalchemy.orm import relationship

from app.models.base import Base


class CallDetail(Base):
    """
    Stores detailed call information after a batch call is completed.
    """

    __tablename__ = "call_details"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String, nullable=False)
    batch_call_id = Column(
        UUID(as_uuid=True), ForeignKey("batch_calls.id", ondelete="CASCADE"), nullable=True
    )
    call_duration = Column(Integer, nullable=True)
    recording_url = Column(Text, nullable=True)
    transcription_url = Column(Text, nullable=True)
    status = Column(String(50), nullable=False)
    call_failing_reason = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # New fields based on the updated proto definition
    type = Column(String(50), nullable=True)
    from_phone = Column(String(50), nullable=True)
    to_phone = Column(String(50), nullable=True)
    transfer_status = Column(String(50), nullable=True)
    additional_guidelines = Column(Text, nullable=True)
    call_metadata = Column(Text, nullable=True)  # JSON stored as text, renamed from 'metadata'
    month = Column(Integer, nullable=True)
    year = Column(Integer, nullable=True)
    billed = Column(Boolean, default=False, nullable=False)
    invoice_id = Column(String, nullable=True)
    agent_id = Column(String(255), nullable=True)  
    avg_latency = Column(Float, nullable=True)
    cost = Column(Float, nullable=True)
    cost_breakdown = Column(JSONB, nullable=True)
    latency_breakdown = Column(JSONB, nullable=True)

    # Relationships
    batch_call = relationship("BatchCall", back_populates="call_details")
    # Note: agent relationship removed due to agent_id being a flexible string field
    # that can contain external agent IDs not present in the agents table

    def __repr__(self):
        return f"<CallDetail id={self.id} batch_call_id={self.batch_call_id}>"
