import uuid
from datetime import datetime

from sqlalchemy import <PERSON><PERSON><PERSON>, Column, DateTime, Float, String
from sqlalchemy.dialects.postgresql import UUID

from app.models.base import Base


class Voice(Base):
    __tablename__ = "voices"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    voice_id = Column(String, nullable=False, unique=True)  # 11labs voice ID
    name = Column(String, nullable=False)
    category = Column(String, nullable=False)
    stability = Column(Float, nullable=False)  # [0.0 - 1.0]
    similarity_boost = Column(Float, nullable=False)  # [0.0 - 1.0]

    # Optional parameters
    style = Column(Float, nullable=True)  # [0.0 - 1.0]
    speed = Column(Float, nullable=True)  # [0.8 - 1.2], default 1.0
    use_speaker_boost = Column(Boolean, nullable=True, default=False)

    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    def __repr__(self):
        return f"<Voice id={self.id}, name={self.name}>"
