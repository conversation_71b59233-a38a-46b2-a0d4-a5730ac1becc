# In app/models.py or wherever Agent model is defined
import uuid
from datetime import datetime
from sqlalchemy import Column, DateTime, ForeignKey, String, Text, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models.base import Base

class Agent(Base):
    __tablename__ = "agents"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String, nullable=False)
    kind = Column(String(20), nullable=False)
    avatar_url = Column(String, nullable=True)
    system_prompt = Column(Text, nullable=False)
    voice_id = Column(UUID(as_uuid=True), ForeignKey("voices.id"), nullable=False)
    csv_metadata = Column(JSON, nullable=True)  # New column for storing CSV metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    
    # Establish relationship with UserAgent junction table.
    users = relationship("UserAgent", back_populates="agent", cascade="all, delete-orphan")
    
    # Relationship with Voice
    voice = relationship("Voice")
    
    # Note: call_details relationship removed since CallDetail.agent_id is now a flexible string field
    # that can contain external agent IDs not present in the agents table

    def __repr__(self):
        return f"<Agent id={self.id}>"