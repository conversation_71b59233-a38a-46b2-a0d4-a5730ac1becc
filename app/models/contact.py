import uuid
from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, String, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models.base import Base


class Contact(Base):
    """
    Stores uploaded contact lists.
    """

    __tablename__ = "contacts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(String, nullable=False)  # Reference User ID without ForeignKey
    title = Column(String, nullable=False)
    csv_url = Column(Text, nullable=False)
    description = Column(Text, nullable=True)
    uploaded_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    agent_id = Column(
        UUID(as_uuid=True), ForeignKey("agents.id", ondelete="SET NULL"), nullable=True
    )

    # Relationship with Agent
    agent = relationship("Agent")
    contact_lists = relationship("ContactList", back_populates="contact", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Contact id={self.id} user_id={self.user_id}>"
