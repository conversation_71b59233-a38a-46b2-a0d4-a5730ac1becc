from datetime import datetime

from sqlalchemy import Column, DateTime, ForeignKey, String
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.models.base import Base


class UserAgent(Base):
    __tablename__ = "user_agents"

    # Store `user_id` as a String (UUID) instead of ForeignKey
    user_id = Column(String, nullable=False, primary_key=True)
    agent_id = Column(
        UUID(as_uuid=True), ForeignKey("agents.id", ondelete="CASCADE"), primary_key=True
    )
    assigned_at = Column(DateTime, default=datetime.utcnow, nullable=False)

    # Define bidirectional relationships.
    agent = relationship("Agent", back_populates="users")

    def __repr__(self):
        return f"<UserAgent user_id={self.user_id} agent_id={self.agent_id}>"
