import os
from typing import Optional

from livekit import api

from app.core.config import settings


def create_room_recording_request(
    room_name: str,
    filename_prefix: str = "recording",
    layout: str = "speaker",
    custom_base_url: Optional[str] = None,
    preset: api.EncodingOptionsPreset = api.EncodingOptionsPreset.H264_720P_30,
    audio_only: bool = True,
    s3_config: Optional[dict] = None,
) -> api.RoomCompositeEgressRequest:
    """
    Create a room composite egress request for recording.

    Args:
        room_name (str): Name of the room to record
        filename_prefix (str, optional): Prefix for output files. Defaults to "recording".
        layout (str, optional): Recording layout. Defaults to "speaker".
        custom_base_url (str, optional): Custom base URL for template. Defaults to None.
        preset (api.EncodingOptionsPreset, optional): Encoding preset. Defaults to H264_720P_30.
        audio_only (bool, optional): Record audio only. Defaults to False.
        s3_config (dict, optional): S3 upload configuration. Defaults to None.

    Returns:
        api.RoomCompositeEgressRequest: Configured recording request
    """
    # Prepare S3 upload configuration
    s3_upload = None
    if s3_config:
        s3_upload = api.S3Upload(
            bucket=s3_config.get("bucket", ""),
            region=s3_config.get("region", ""),
            access_key=s3_config.get("access_key", ""),
            secret=s3_config.get("secret", ""),
            force_path_style=s3_config.get("force_path_style", True),
        )

    # Create segment output
    segment_output = api.SegmentedFileOutput(
        filename_prefix=filename_prefix,
        playlist_name=f"{filename_prefix}.m3u8",
        live_playlist_name=f"{filename_prefix}-live.m3u8",
        segment_duration=2,
        s3=s3_upload,
    )

    # Create and return recording request
    return api.RoomCompositeEgressRequest(
        room_name=room_name,
        layout=layout,
        custom_base_url=custom_base_url,
        preset=preset,
        audio_only=audio_only,
        segment_outputs=[segment_output],
    )


def get_recording_config_from_env() -> dict:
    """
    Retrieve S3 recording configuration from environment variables.

    Returns:
        dict: S3 configuration dictionary
    """
    return {
        "bucket": settings.S3_BUCKET,
        "region": settings.S3_REGION,
        "access_key": settings.S3_ACCESS_KEY,
        "secret": settings.S3_SECRET_KEY,
        "force_path_style": settings.S3_FORCE_PATH_STYLE.lower() == "true",
    }


async def start_room_recording(lk_api: api.LiveKitAPI, room_name: str, **kwargs):
    """
    Start a room recording using the provided LiveKit API client.

    Args:
        lk_api (api.LiveKitAPI): LiveKit API client
        room_name (str): Name of the room to record
        **kwargs: Additional arguments to pass to create_room_recording_request

    """
    # Create recording request
    recording_request = create_room_recording_request(room_name, **kwargs)

    try:
        # Start recording
        return await lk_api.egress.start_room_composite_egress(recording_request)
    except Exception as e:
        print(f"Recording start failed: {e}")
        raise
