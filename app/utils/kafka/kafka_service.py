import logging
import json
from typing import Dict, Any
from app.utils.constants.send_email_type_enum import SendEmailTypeEnum
from app.utils.kafka.kafka_client import KafkaClient


class KafkaProducer:
    def __init__(self):
        """Initialize the KafkaProducer with KafkaClient for connection management."""
        self.logger = logging.getLogger(__name__)
        self.kafka_client = KafkaClient()
        self.producer = self.kafka_client.connect_producer()

    def __del__(self):
        """Destructor to close the connection using KafkaClient."""
        self.kafka_client.close()

    def send_email_event(
        self, email_type: SendEmailTypeEnum, email: str, name: str, otp: str
    ) -> Dict[str, Any]:
        """
        Emits an event to send an email via Kafka with a specified topic.

        Args:
            email_type: Type of email to send
            email: Recipient email address
            name: Recipient name
            otp: One-time password
            topic: Kafka topic to publish the event

        Returns:
            Dict with success status and message
        """
        try:
            message = {
                "action": "sendEmail",
                "data": {
                    "emailId": email,
                    "userName": name,
                    "otp": otp,
                },
            }

            # Convert message to JSON string
            message_bytes = json.dumps(message)

            # Send message to specified Kafka topic
            future = self.producer.send(topic=email_type, value=message_bytes)

            # Block until message is sent (or timeout)
            future.get(timeout=10)

            return {"success": True, "message": f"Email event published to topic '{email_type}'."}
        except Exception as error:
            self.logger.error(f"send_email_event error - {str(error)}")
            return {"success": False, "message": str(error)}
